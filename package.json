{"name": "merchant-offline-pos-ui", "version": "5.11.0", "description": "Front end project for Aplazo Merchants's offline POS", "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://posui.aplazo.mx", "email": "<EMAIL>"}, "homepage": "https://posui.aplazo.mx", "repository": {"type": "git", "url": "https://github.com/aplazo/angular.merchant-offline-pos-ui"}, "engines": {"node": ">=22.12.0", "angular": ">=17.3.0"}, "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --configuration dev", "dev:dev": "ng serve --host 0.0.0.0 --configuration dev", "dev:stg": "ng serve --host 0.0.0.0 --configuration stage", "dev:prod": "ng serve --host 0.0.0.0 --configuration production", "build": "ng build --configuration production", "test": "ng test --code-coverage --no-watch --no-progress --browsers=ChromeHeadlessNoSandbox", "test:watch": "ng test --code-coverage", "e2e": "CI=true playwright test", "lint": "ng lint", "prettier": "prettier --write src/app/**/*.{ts,js,css,html}", "prepare": "husky"}, "private": true, "dependencies": {"@angular/common": "~17.3.0", "@angular/compiler": "~17.3.0", "@angular/core": "~17.3.0", "@angular/forms": "~17.3.0", "@angular/platform-browser": "~17.3.0", "@angular/platform-browser-dynamic": "~17.3.0", "@angular/router": "~17.3.0", "@angular/service-worker": "~17.3.0", "@aplazo/front-analytics": "~2.23.0", "@aplazo/front-observability": "~2.23.0", "@aplazo/i18n": "~2.23.0", "@aplazo/merchant": "~2.23.0", "@aplazo/shared-ui": "~2.23.0", "@aplazo/ui-icons": "~2.23.0", "@datadog/browser-logs": "~6.6.0", "@datadog/browser-rum": "~6.6.0", "@jsverse/transloco": "~7.4.0", "@kount/kount-web-client-sdk": "^2.2.0", "@ngneat/dialog": "~5.1.0", "@statsig/angular-bindings": "~3.16.2", "@statsig/session-replay": "~3.16.2", "@statsig/web-analytics": "~3.16.2", "date-fns": "~4.1.0", "date-fns-tz": "^3.1.3", "file-saver": "^2.0.5", "graphql-request": "^6.1.0", "nanoid": "5.0.8", "ngx-mask": "~17.0.7", "ngx-toastr": "~19.0.0", "node-fetch": "^3.3.2", "rxjs": "~7.8.0", "tslib": "^2.6.2", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "~17.3.0", "@angular-eslint/builder": "~17.3.0", "@angular-eslint/eslint-plugin": "17.3.0", "@angular-eslint/eslint-plugin-template": "17.3.0", "@angular-eslint/schematics": "~17.3.0", "@angular-eslint/template-parser": "17.3.0", "@angular/animations": "~17.3.0", "@angular/cli": "~17.3.0", "@angular/compiler-cli": "~17.3.0", "@angular/language-service": "~17.3.0", "@angular/localize": "~17.3.0", "@auth0/angular-jwt": "~5.2.0", "@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@justinribeiro/lite-youtube": "1.7.1", "@ngx-env/builder": "^17.0.0", "@playwright/test": "1.49.1", "@types/eslint": "^8.4.5", "@types/jasmine": "~5.1.0", "@types/node": "^20.11.30", "@typescript-eslint/eslint-plugin": "6.13.1", "@typescript-eslint/parser": "6.13.1", "air-datepicker": "3.4.0", "angular-google-tag-manager": "~1.9.0", "autoprefixer": "^10.4.16", "chart.js": "~3.6.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^9.1.7", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng2-charts": "~3.0.11", "postcss": "8.4.32", "prettier": "~3.1.1", "prettier-eslint": "^16.1.2", "pretty-quick": "^4.0.0", "tailwindcss": "3.4.15", "ts-node": "^10.9.2", "typescript": "~5.4.5", "webextension-polyfill": "^0.8.0", "xlsx": "~0.18.5"}}