# Change Log

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/)
and this project adheres to [Semantic Versioning](http://semver.org/).

## [5.11.0]

- [MEXP-597](https://aplazo.atlassian.net/browse/MEXP-597) - add new material pop form component
- [MEXP-607](https://aplazo.atlassian.net/browse/MEXP-607) - add raffle section for premios aplazo

## [5.10.0-RELEASE] - 2025-06-23

- [MEXP-620](https://aplazo.atlassian.net/browse/MEXP-620) - fix timestamp type and add aplazoversity analytics events

## [5.9.0-RELEASE] - 2025-06-16

- [MEXP-602](https://aplazo.atlassian.net/browse/MEXP-602) - new loader component

## [5.8.0-RELEASE] - 2025-06-05

- [MEXP-507](https://aplazo.atlassian.net/browse/MEXP-507) - new error messages from websocket
- [MEXP-488](https://aplazo.atlassian.net/browse/MEXP-488) - add PWA capabilities

## [5.7.0-RELEASE] - 2025-05-19

- [MEXP-580](https://aplazo.atlassian.net/browse/MEXP-580) - Change statsig identifiers

## [5.6.0-RELEASE] - 2025-05-15

- [MEXP-558](https://aplazo.atlassian.net/browse/MEXP-558) - Change statsig feature flags to native library

## [5.5.0-RELEASE] - 2025-05-13

- [MEXP-508](https://aplazo.atlassian.net/browse/MEXP-508) - Add new feature for dynamic banners

## [5.4.1-RELEASE] - 2025-04-30

- [MEXP-529](https://aplazo.atlassian.net/browse/MEXP-529) - Add max lenght product name input

## [5.4.0-RELEASE] - 2025-04-29

- [MEXP-519](https://aplazo.atlassian.net/browse/MEXP-519) - Tiers for premios aplazo winners

## [5.3.2-RELEASE] - 2025-04-22

- [MEXP-517](https://aplazo.atlassian.net/browse/MEXP-517) - fix premios aplazo css issues

## [5.3.1-RELEASE] - 2025-04-21

- [MEXP-510](https://aplazo.atlassian.net/browse/MEXP-510) - Fix Kustomer

## [5.3.0-RELEASE] - 2025-04-16

- [MEXP-510](https://aplazo.atlassian.net/browse/MEXP-510) - Kustomer improvements

## [5.2.0-RELEASE] - 2025-04-01

- [MEXP-477](https://aplazo.atlassian.net/browse/MEXP-477) - Enable payment method by Barcode

## [5.1.0-RELEASE] - 2025-03-20

- [MEXP-335](https://aplazo.atlassian.net/browse/MEXP-335) - consume env variables from .env file
- fix delay in loading the app when the feature flags are waiting for error or failure status

## [5.0.2-RELEASE] - 2025-03-11

- [MEXP-382](https://aplazo.atlassian.net/browse/MEXP-382) - send branch id fo merchant-promotions

## [5.0.1-RELEASE] - 2025-02-28

- [MEXP-427](https://aplazo.atlassian.net/browse/MEXP-427) - fix bug when change share link tab then the qr is not visible

## [5.0.0-RELEASE] - 2025-02-26

- [MEXP-309](https://aplazo.atlassian.net/browse/MEXP-309) - Migration from Angular 15.2.10 to Angular 17.3.0

### Breaking Changes

- Migration from Angular 15.2.10 to Angular 17.3.0
- Updated Node.js requirement to >=22.12.0
- Updated all @angular/\* packages to ~17.3.0
- Updated all @aplazo/\* packages to ~2.10.0
- Major dependencies updates:
  - RxJS 7.8.0
  - Zone.js 0.14.10
  - TypeScript 5.4.5
  - @jsverse/transloco 7.4.0 (replacing @ngneat/transloco)
  - ngx-mask 17.0.7
  - ngx-toastr 19.0.0
  - date-fns 4.1.0

### Added

- Standalone components as default architecture
- Deferrable views support
- Enhanced build optimization
- Improved type checking with latest TypeScript

### Changed

- Updated testing framework and configurations
- Improved bundle optimization strategies
- Enhanced dependency injection system
- Updated ESLint and Prettier configurations
- Migrated from @ngneat/transloco to @jsverse/transloco

### Removed

- Legacy View Engine support
- Deprecated APIs and patterns

## [4.12.1-RELEASE] - 2025-02-26

- [MEXP-391](https://aplazo.atlassian.net/browse/MEXP-391) - copies
- [MEXP-392](https://aplazo.atlassian.net/browse/MEXP-392) - copies
- [MEXP-393](https://aplazo.atlassian.net/browse/MEXP-393) - copies
- [MEXP-325](https://aplazo.atlassian.net/browse/MEXP-325) - copies

## [4.12.0-RELEASE] - 2025-02-18

- [MEXP-323](https://aplazo.atlassian.net/browse/MEXP-323) - Fix logo size and preconnect to cdn.aplazo.mx
- [MEXP-354](https://aplazo.atlassian.net/browse/MEXP-354) - Fix terms and conditions link

## [4.11.0-RELEASE] - 2025-02-12

- [MEXP-299](https://aplazo.atlassian.net/browse/MEXP-299) - Fix throttle time for send qr code

## [4.10.0-RELEASE] - 2025-02-11

- [MEXP-299](https://aplazo.atlassian.net/browse/MEXP-299) - Feature send qr code

## [4.9.0-RELEASE] - 2025-02-07

- [MEXP-285](https://aplazo.atlassian.net/browse/MEXP-285) - Fix notifications tracking analytics event

## [4.8.0-RELEASE] - 2025-02-06

- [MEXP-316](https://aplazo.atlassian.net/browse/MEXP-316) - Fix Video position in the layout in landing page

## [4.7.0-RELEASE] - 2025-01-31

- [MEXP-222](https://aplazo.atlassian.net/browse/MEXP-222) - HALO (prepaid loans)

## [4.6.0-RELEASE] - 2025-01-22

- [MEXP-266](https://aplazo.atlassian.net/browse/MEXP-266) - Kount's sessionId to NIP

## [4.5.1-RELEASE] - 2025-01-14

- [MEXP-191](https://aplazo.atlassian.net/browse/MEXP-191) - fix tabs issues

## [4.5.0-RELEASE] - 2025-01-10

- [MEXP-193](https://aplazo.atlassian.net/browse/MEXP-193) - fix bootstrapping delay issues

## [4.4.1-RELEASE] - 2024-12-18

- [MEXP-188](https://aplazo.atlassian.net/browse/MEXP-188) - fix datepicker calendar border on active/focus

## [4.4.0-RELEASE] - 2024-12-17

- [MEXP-186](https://aplazo.atlassian.net/browse/MEXP-186) - Refresh loan status GA4

## [4.3.0-RELEASE] - 2024-12-12

- [MEXP-41](https://aplazo.atlassian.net/browse/MEXP-41) - Premios Aplazo / always uppercase for code cashier
- [MEXP-63](https://aplazo.atlassian.net/browse/MEXP-63) - Premios Aplazo / GA4 onboarding
- [MEXP-126](https://aplazo.atlassian.net/browse/MEXP-126) - Premios Aplazo / GA4 position
- [MEXP-77](https://aplazo.atlassian.net/browse/MEXP-77) - improve datepicker component
- [MEXP-182](https://aplazo.atlassian.net/browse/MEXP-182) - KLO - connectionStatusService improvements

## [4.2.1-RELEASE] - 2024-11-28

- [MEXP-179](https://aplazo.atlassian.net/browse/MEXP-179) - Premios Aplazo / dynamic TyC URL link

## [4.2.0-RELEASE] - 2024-11-22

- [MEXP-34](https://aplazo.atlassian.net/browse/MEXP-34) - Premios Aplazo / Mi posicion section
- [MEXP-114](https://aplazo.atlassian.net/browse/MEXP-114) - Premios Aplazo / Mi posicion search bar
- [MEXP-115](https://aplazo.atlassian.net/browse/MEXP-115) - Premios Aplazo / participant position section
- [MEXP-117](https://aplazo.atlassian.net/browse/MEXP-117) - Premios Aplazo / clear participant button
-

## [4.1.1-RELEASE] - 2024-11-11

- [MEXP-110](https://aplazo.atlassian.net/browse/MEXP-110) - fix error messages for OTP validation

## [4.1.0-RELEASE] - 2024-11-08

- [MEXP-33](https://aplazo.atlassian.net/browse/MEXP-33) - add new "Premios Aplazo" module

## [4.0.2-RELEASE] - 2024-11-04

- fix LCP
- [MEXP-141](https://aplazo.atlassian.net/browse/MEXP-141) - fix statsig offline bug

## [4.0.1-RELEASE] - 2024-11-01

- fix statsig timeout

## [4.0.0-RELEASE] - 2024-10-29

- [MEXP-93](https://aplazo.atlassian.net/browse/MEXP-93) - validate user agent during app initialization
- SSE event to hydrate orders
- [MEXP-70](https://aplazo.atlassian.net/browse/MEXP-70) - migrate feature flags to new library
- [MEXP-106](https://aplazo.atlassian.net/browse/MEXP-106) - add new feature flag for SSE
- [MEXP-110](https://aplazo.atlassian.net/browse/MEXP-110) - redo auth code feature
- [MEXP-66](https://aplazo.atlassian.net/browse/MEXP-66) - add feature to validate OTP from backend service
- [MEXP-111](https://aplazo.atlassian.net/browse/MEXP-111) - add feature flag for OTP validation

## [3.15.1-RELEASE] - 2024-10-09

- hasOwn not supported in old browsers
- jenkins pipeline improvements
- [APM-2764](https://aplazo.atlassian.net/browse/APM-2764) - fix stream leaking within settings component
- [APM-2671](https://aplazo.atlassian.net/browse/APM-2671) - fix styles for share payment link styles

## [3.15.0-RELEASE] - 2024-08-15

- POS UI Challenges - New module for Hackathon project

## [3.14.0-RELEASE]

- [APM-2448](https://aplazo.atlassian.net/browse/APM-2488) - Change search component for a box style

## [3.13.0-RELEASE] - 2024-08-09

- [APM-2615](https://aplazo.atlassian.net/browse/APM-2615) -[POSUI] - Loan in status CANCELLED is not see correctly in dashboard
- [APM-2647](https://aplazo.atlassian.net/browse/APM-2647) - [POSUI] - Enable feature flag by Github for POSUI Express

## [3.12.1-RELEASE] - 2024-08-07

- fix send checkout link

## [3.12.0-RELEASE] - 2024-08-07

- [APM-2528](https://aplazo.atlassian.net/browse/APM-2528) - Sidebar menu click outside improvements.
- [APM-2201](https://aplazo.atlassian.net/browse/APM-2201) - improve unit testing
-

## [3.11.0-RELEASE] - 2024-07-04

- [APM-2468](https://aplazo.atlassian.net/browse/APM-2468) - Refresh loan status for order page view.

## [3.10.0-RELEASE] - 2024-06-27

- update register form link for prod environment
- [APM-2481](https://aplazo.atlassian.net/browse/APM-2481) - Update login form i18n
- [APM-2478](https://aplazo.atlassian.net/browse/APM-2478) - improve stat cards
- [APM-2476](https://aplazo.atlassian.net/browse/APM-2476) - Align tool-tip to headlines

## [3.9.0-RELEASE] - 2024-06-06

- Add new finish order flow
- [APM-2431](https://aplazo.atlassian.net/browse/APM-2431) - Add banner into layout
- [APM-2442](https://aplazo.atlassian.net/browse/APM-2442) - set dynamic text for the banner

## [3.8.2-RELEASE] - 2024-03-25

- adjust sample rate of Datadog's sessions
- retrieve and used only the necesary flags for the project

## [3.8.1- RELEASE] - 2024-03-06

- [APM-2051](https://aplazo.atlassian.net/browse/APM-2051) - Se modificar ubicación de elementos en el DOM para el responsive
- [APM-2178](https://aplazo.atlassian.net/browse/APM-2178) - Remove radio button feature flag

## [3.8.0-RELEASE] - 2024-02-06

- [APM-2093](https://aplazo.atlassian.net/browse/APM-2093) - POSUI - Remove Kustomer Flag

### Changed

- Add new Feature Flag to handle the usecase of login in the Aplazo's VPN migration
- Replace api subdomain for api.aplazo.\*\*\*

## [3.7.0-RELEASE] - 2024-01-30

- [APM-2019](https://aplazo.atlassian.net/browse/APM-2019) - POSUI express order + static QR

## [3.6.1-RELEASE] - 2023-12-15

- remove strict lowercase from login username validation

## [3.6.0-RELEASE] - 2023-12-14

- [APM-1991](https://aplazo.atlassian.net/browse/APM-1991) - POSUI - Error en el filtrado del apartado historico
- [APM-1992](https://aplazo.atlassian.net/browse/APM-1992) - POSUI - Default contact option
- [APM-2000](https://aplazo.atlassian.net/browse/APM-2000) - login username should be case insensitive

### Changed

- improve [`orders service`](src/app/core/services/socket.service.ts) - to handle in a better way the closing events

## [3.5.0-RELEASE] - 2023-12-06

- [APM-1987](https://aplazo.atlassian.net/browse/APM-1987) - POSUI : Look and feel improvements
- Hackathon ideas

### Changed

- refactor kustomer render logic

## [3.4.0-RELEASE] - 2023-11-22

- [APM-1745](https://aplazo.atlassian.net/browse/APM-1975) - POSUI - Integrate V2 data events

## [3.3.1-RELEASE] - 2023-11-09

- send merchantId, merchantName, branchId and branchName to google analytics

## [3.3.0-RELEASE] - 2023-11-08

### Added

- [APM-1928](https://aplazo.atlassian.net/browse/APM-1928) - Posui - Frontend add data event
- [APM-1923](https://aplazo.atlassian.net/browse/APM-1923) - Add default handler for scenarios where Split.io service's down

### Removed

- cypress e2e library

## [3.2.0-RELEASE] - 2023-11-06

- [APM-1877](https://aplazo.atlassian.net/browse/APM-1877) - add google analytics events
- [APM-1906](https://aplazo.atlassian.net/browse/APM-1906) - add more google analytics events

## [3.1.0-RELEASE] - 2023-10-24

- [APM-1872](https://aplazo.atlassian.net/browse/APM-1872) - POSUI express order
- [APM-1888](https://aplazo.atlassian.net/browse/APM-1888) - fix cancel loan bug

### Added

- [`express form component`](src/app/modules/cart-v1/express-form/express-form.component.ts) - component that shows the express form
- [`handle order generation service`](src/app/modules/cart-v1/handle-order-generation.service.ts) - to handle the logic to open old flow or new flow in terms of feature flag

### Changed

- [`orders component`](src/app/modules/orders-v1/orders-v1.component.ts) - add new order handler and connect with click event
- [`historical component`](src/app/modules/historical-v1/historical-v1.component.ts) - add new order handler and connect with click event
- [`product form`](src/app/modules/cart-v1/product-form/product-form.component.ts) - change location of the files
- delete `partytown` as not propagate correctly the gtag events

## [3.0.1-RELEASE] - 2023-10-16

- [APM-1869](https://aplazo.atlassian.net/browse/APM-1869) - [Front end] POSUI - Tech debt - Log-In Errors

## [3.0.0-RELEASE] - 2023-10-09

### Changed

- update angular to version `15`
- update transloco to version `4.3.0`
- refactor to use standalone components
- set the new strategy to consume QR image from backend

### Added

- add tailwindcss
- add `@aplazo/front-analytics`
- add `@aplazo/front-feature-flags`
- add `@aplazo/front-observability`
- add `@aplazo/merchant`
- add `@aplazo/shared-ui`
- add `@aplazo/ui-icons`

### Removed

- remove `@aplazo/merchant-ui`
- remove `@aplazo/merchant-utils`
- remove `@aplazo/web-ui`
- remove `@angular/material`
- remove `@angular/cdk`
- remove `@angular/broswer/animations`
- remove `qrcode`

## [2.5.2-RELEASE] - 2023-08-21

- fix max loan amount for wm-posui flow

## [2.5.1-RELEASE] - 2023-07-28

- fix success message for wm loan creation

## [2.5.0-RELEASE] - 2023-07-26

- [APM-1592](https://aplazo.atlassian.net/browse/APM-1592) - Add extra validation to the login usecase for prevent access to user with unallowed roles
- [APM-1684](https://aplazo.atlassian.net/browse/APM-1684) - Change Datadog's tracking sessions percentage

### Changed

- update `@aplazo/merchant-ui` to `3.0.0`
- update `@aplazo/merchant-utils` to `2.0.0`
- [`appModule`](src/app/app.module.ts) - to adjust datadog configuration
- [`loginUsecase`](src/app/modules/newAuth/login/login.usecase.ts) - refactor to login usecase to handle the new roles
- rewrite [`storeService`](src/app/core/services/store.service.ts) - to remove extra boiler plate
- [`socketService`](src/app/core/services/socket.service.ts) - to remove dependencies with local jwt decoder

### Added

- [`RuntimeError`](src/app/core/domain/runtime-error.ts) - to handle the runtime errors and add custom attributes to the error
- [`refundInfoModule`](src/app/modules/shared/molecules/refund-info/refund-info.module.ts) - to handle the refund info dialog

## [2.4.1-RELEASE] - 2023-07-18

- fix loan status for orders

## [2.4.0-RELEASE] - 2023-06-20

### Changed

- update `@aplazo/merchant-ui` to `2.2.0`
- update `@aplazo/web-ui` to `3.0.0`
- update `@aplazo/merchant-utils` to `1.11.0`
- improve [`notifications`](src/app/core/domain/notification.ts) to handle the changes from service
- add summary to [`historicalComponent`](src/app/modules/historical-v1/historical.component.ts)
- [`historicalComponent`](src/app/modules/historical-v1/historical.component.ts) - add a button to download the historical report in xlsx format

### Added

- [`getHistoricalReportUsecase`](src/app/modules/historical-v1/get-historical-report.usecase.ts) - to allow the user download a report of the historical data

## [2.3.3-RELEASE] - 2023-05-19

- allow to filter notifications by merchantId on the frontend instead of the backend

## [2.3.2-RELEASE] - 2023-03-31

- set new auth api url for prod environment

## [2.3.1-RELEASE] - 2023-03-30

- fix type error within top-handler error

## [2.3.0-RELEASE] - 2023-03-29

- remove lazy loading strategy from important modules
- improve [`Dockerfile`](Dockerfile) to use `npm ci` instead of `npm install`
- improve [`nginx-custom.conf`](nginx-custom.conf) to add `Cache Control header`
- top error handler to handle load chunk errors
- add `@aplazo/front-observability` to handle the observability of the application with `Datadog`
- fix the login issue to show the loader component until finished navigation or an error is throwed

## [2.2.0-RELEASE] - 2023-03-08

- [APM-1202](https://aplazo.atlassian.net/browse/APM-1202) - Modify guards to allow new Roles (manager,sell_agent) to use de app.

- [APM-1271](https://aplazo.atlassian.net/browse/APM-1271) - Add Google Analytics
- [APM-1277](https://aplazo.atlassian.net/browse/APM-1277) - AplazoVersity
- [APM-1293](https://aplazo.atlassian.net/browse/APM-1293) - retrieve data for media content from the backend

### Added

- `@aplazo/front-feature-flags` - to handle the feature flags
- [`mediaContentCardModule`](src/app/modules/shared/molecules/media-content-card/media-content-card.module.ts) - Media content section (AplazoVersity)
- [`mediaContentModule`](src/app/modules/media-content/media-content.module.ts) - Media content section (AplazoVersity)
- [`videoCardModule`](src/app/modules/shared/molecules/video-card/video-card.module.ts) - To show the video player within a dialog component
- [`youtubePlayerModule`](src/app/modules/shared/molecules/youtube-player/youtube-player.module.ts) - To handle the render of a youtube video
- [`getTrainingMediaContentUsecase`](src/app/core/application/get-training-media-content.usecase.ts) - To put the domain logic for retrieve info outside a component

### Fixed

- There is an automatic redirection to checkout outside the page when order is created. The bug was originated for an incomplete response of the branch definition within the [`finishOrderUsecase`](src/app/modules/cart-v1/finish-order.usecase.ts)

### Changed

- add scoped registry within Dockerfile
- set `@auth0/angular-jwt` to exact version `5.0.2`
- update `@aplazo/merchant-ui` to `1.8.0`
- update `@aplazo/utils` to `1.9.0`

## [2.1.3-RELEASE] - 2022-12-01

- [APM-1134](https://aplazo.atlassian.net/browse/APM-1134) - improve notifications route

### Added

- [`notificationsStore`](src/app/core/services/notifications-store.service.ts)

### Changed

- [`notificationsV1Component`](src/app/modules/notifications-v1/notifications-v1.component.ts)
- [`securedV1Component`](src/app/modules/secured-v1/secured-v1.component.ts)

---

## [2.1.2-RELEASE] - 2022-11-08

- [APM-1098](https://aplazo.atlassian.net/browse/APM-1098) - Add notifications icon into sidenav link
- [APM-1086](https://aplazo.atlassian.net/browse/APM-1086) - Disable send button within send payment link for pending loans
- [APM-1124](https://aplazo.atlassian.net/browse/APM-1124) - show an offline alert to the user when there is no internet connection

### Changed

- upgrade `@aplazo/merchant-utils` to `1.5.0`
- [`promosService`](src/app/modules/promos-v1/promos.service.ts) - move into the core module to allow sharing across multiple modules
- [`shareLinkComponent`](src/app/modules/share-payment-link/share-payment-link.component.ts) - shareLink component acquiere more responsabilities to handle the business logic
- [`sentWhatsappComponent`](src/app/modules/share-payment-link/sent-whatsapp/sent-whatsapp.component.ts) - reduce the responsibilities of sentWhatsapp component to convert into a dumb component
- [`disallowedNavigationWhenOfflineGuard`](src/app/core/guards/disallow-navigation-when-offline.guard.ts)

---

## [2.1.1-RELEASE] - 2022-11-04

- [APM-1123](https://aplazo.atlassian.net/browse/APM-1123) - order list is not update correctly

### Fixed

- [`socketService`](src/app/core/services/socket.service.ts) - to handle all events from webSocket

---

## [2.1.0-RELEASE] - 2022-11-01

- [APM-984](https://aplazo.atlassian.net/browse/APM-984) - Today sales report
- [APM-985](https://aplazo.atlassian.net/browse/APM-985) - Feature flag for today sales report
- [APM-1087](https://aplazo.atlassian.net/browse/APM-1087) - Notifications module
- [APM-1077](https://aplazo.atlassian.net/browse/APM-1077) - hide branch selection when a user is logged
- [APM-1078](https://aplazo.atlassian.net/browse/APM-1078) - Does not allow creating new branches with duplicate names
- [APM-1079](https://aplazo.atlassian.net/browse/APM-1079) - Hide change branch option from menu
- [APM-1082](https://aplazo.atlassian.net/browse/APM-1082) - add feature flag for handle the branch creation
- [APM-1034](https://aplazo.atlassian.net/browse/APM-1034) - show a summary for today orders

#### Added

- [`promosModule`](src/app/modules/promos-v1/promos-v1.module.ts) - work with module to show a table that shows the promos that are associated with a _*merchantId*_.
- [`hasEmptyBranchSelectedGuard`](src/app/core/guards/has-empty-branch-selected.guard.ts) - guard to prevent a logged user to access branch selection page
- [`hasBranchCreationEnabledGuard`](src/app/core/guards/has-branch-creation-enabled.guard.ts) - guard to prevent that a logged user can access to branch creation page

### Changed

- upgrade `@aplazo/merchant-ui` to `1.4.0`
- upgrade `@aplazo/merchant-utils` to `1.4.0`
- upgrade `@aplazo/partner-styles` to `2.1.0`
- upgrade `@aplazo/web-ui` to `2.6.0`
- scoped i18n resolution for `@ngneat/transloco`
- skiped global error handler interceptor for `svg` & `json` requests
- retrieve `translation` files from [`i18n`](https://github.com/aplazo/front.in18/tree/master/posui)
- add strategy to get a `translation` file from inside repo on external petition error
- improve [`loginUsecase`](src/app/modules/newAuth/login/login.usecase.ts) - for new Auth flow
- improve [`loginService`](src/app/modules/newAuth/login/login.service.ts) - for new Auth flow
- improve [`loginComponent`](src/app/modules/newAuth/login/login.component.ts) - for new Auth flow
- hide change storefront from principal layout [`securedV1Component`](src/app/modules/secured-v1/secured-v1.component.ts)
- add to [`storeService`](src/app/core/services/socket.service.ts) the variables to handle new functionalities
- improve [`ordersV1Component`](src/app/modules/orders-v1/orders-v1.component.ts) - for show summary component
- adding into [`appRoutingModule`](src/app/app-routing.module.ts) new guards for handle the navigation logic
- improve the error handling logic to retrieve the image URL path from the main layout menu button.
- pull merchant name from backend (merchant details)

---

## [2.0.5-RELEASE] - 2022/10/20

### Changed

- Change logic for visibility of app's new version
- Change whatsapp phone number for merchant support

---

## [2.0.4-RELEASE] - 2022/10/01

### Fixed

- allowed all branches navigate to new version of POSUI

---

## [2.0.3-RELEASE] - 2022/09/06

### Fixed

- fix sended parameter `merchantId` from [`tagManagerService`](src/app/core/services/data-tag-manager.service.ts)

---

## [2.0.2-RELEASE] - 2022/09/02

### Fixed

- Placeholder from add product form [`productForm`](src/assets/i18n/es.json)

---

## [2.0.1-RELEASE] - 2022/09/02

### Fixed

- Orders label selection in [`ordersV1Component`](src/app/modules/orders-v1/orders-v1.component.ts)

---

## [2.0.0-RELEASE] - 2022/09/01

Version prepared for MVP (Minimum Viable Product)

- hiding promotions and help sections
- with a new version Feature Flag for incremental migration
- hiding share payment link to prevetn security leaks

### Breaking changes

- Route tree
- How to consume routes definitions
- How to consume environment
- Prefer reactive over sync/imperative resolution
- Centralize all variables in StoreService (moving from native sessionStorage to Reactive variables)
- Lazy loading modules
- Prefer Single Component Angular Module for Shared components
- Init migration from logic within component to Use cases
- Total rewrite of socket service, there is no necesary to alternate between AJAX request and Websocket emission
- Deprecated the dependencies of `aplazo-pop-up` implementation
- Refresh and reHydration of the state with `APP_INITIALIZER`
- Deprecate `Date().getTime()` to add ID's use `nanoid` instead
- `aplazo-libraries` dependencies was all removed
- use a central loader component
- move almost every interface(_model_), enum or class to `core/domain` folder
- for styles prefer `classes` over `inline-styles`, in fact we want to avoid the use `inline-styles`

- [APM-803](https://aplazo.atlassian.net/browse/APM-803) - Add unit tests for some files
- [APM-763](https://aplazo.atlassian.net/browse/APM-763) - Login screen
- [APM-764](https://aplazo.atlassian.net/browse/APM-764) - Login screen for mobile
- [APM-805](https://aplazo.atlassian.net/browse/APM-805) - e2e for login screen
- [APM-765](https://aplazo.atlassian.net/browse/APM-765) - Select branch
- [APM-766](https://aplazo.atlassian.net/browse/APM-766) - Select branch for mobile
- [APM-771](https://aplazo.atlassian.net/browse/APM-771) - Orders - empty list
- [APM-772](https://aplazo.atlassian.net/browse/APM-772) - Orders - current and historical orders
- [APM-773](https://aplazo.atlassian.net/browse/APM-773) - new Order - page layout
- [APM-774](https://aplazo.atlassian.net/browse/APM-774) - new Order - add product to cart
- [APM-769](https://aplazo.atlassian.net/browse/APM-769) - Left menu Options
- [APM-770](https://aplazo.atlassian.net/browse/APM-770) - Left menu Options mobile
- [APM-795](https://aplazo.atlassian.net/browse/APM-795) - new Order - finish Order
- [APM-775](https://aplazo.atlassian.net/browse/APM-775) - new Order - edit Order
- [APM-776](https://aplazo.atlassian.net/browse/APM-776) - new Order - delete Order
- [APM-796](https://aplazo.atlassian.net/browse/APM-796) - Orders - change status

### Added

1. husky library and precommit hook
1. cypress e2e library and `login.cy.ts`
1. `access-token` interface
1. `pos-app-routes` type, token and implementation
1. `pos-environment` type, token and implementation
1. `credential` interface
1. `browser-temporal-persistence` service
1. `login-module` for auth new routes
1. `forgot-password-module` for auth new routes
1. `select-branch-module` for secured routes
1. `new-branch-module` for secured routes
1. `configuration-module` for secured routes
1. `settings-service` for handle the principal variables over a replaySubject
1. `secured-v1-module` altern secured routing module for nesting the new routes
1. `orders-v1-module` v1 of orders
1. `cart-v1-module` v1 of cart
1. `help-v1-module` v1 of help
1. `settings-v1-module` v1 of settings
1. `promotions-v1-module` v1 of promotions
1. `socket-service` for improve the implementation of the webSocket service
1. `historical-v1-module` v1 of historical orders
1. console log current version of POSUI

### Changed

1. Upgrade angular version from v11 to v13
1. Remove TsLint
1. Add Eslint
1. Fix linter problems accross project
1. Increment build budgets for dev and stage environments
1. Add prettier as a code formatter
1. move guards from `aplazo-libraries` to local development
1. move components from `aplazo-libraries` to local development
1. move styles from `aplazo-libraries` to local development
1. move select branch tools into its own module with a better separation of concerns
1. `secured-component` has a removal of unused code
1. `share-payment-link` change from inputs to receive inject data from matdialog
1. `input` component to SCAM
1. `product-form` component to SCAM & inject data from MAT_DIALOG_DATA

---
