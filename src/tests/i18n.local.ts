import { makeEnvironmentProviders } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import { of } from 'rxjs';
import barcodePayment from '../assets/i18n/barcode-payment/es.json';
import cart from '../assets/i18n/cart/es.json';
import contestEntries from '../assets/i18n/contest-entries/es.json';
import errors from '../assets/i18n/errors/es.json';
import unknown from '../assets/i18n/es.json';
import historical from '../assets/i18n/historical/es.json';
import login from '../assets/i18n/login/es.json';
import maintenance from '../assets/i18n/maintenance/es.json';
import newBranch from '../assets/i18n/new-branch/es.json';
import orderGeneration from '../assets/i18n/order-generation/es.json';
import orders from '../assets/i18n/orders/es.json';
import principalLayout from '../assets/i18n/principal-layout/es.json';
import promos from '../assets/i18n/promos/es.json';
import selectBranch from '../assets/i18n/select-branch/es.json';
import storefrontHelp from '../assets/i18n/storefront-help/es.json';

const NAME_MAPPING = {
  'select-branch': selectBranch,
  cart: cart,
  'contest-entries': contestEntries,
  errors: errors,
  historical: historical,
  login: login,
  maintenance: maintenance,
  'new-branch': newBranch,
  'order-generation': orderGeneration,
  orders: orders,
  'principal-layout': principalLayout,
  promos: promos,
  default: unknown,
  'barcode-payment': barcodePayment,
  'storefront-help': storefrontHelp,
  '': unknown,
} as const;

export type I18nScopeTesting = keyof typeof NAME_MAPPING;

export function provideI18NTesting(scope: I18nScopeTesting = '') {
  const text = NAME_MAPPING[scope];

  const i18nLocal = {
    getTranslateObjectByKey: ({ key }: { key: string }) =>
      of(text[key as keyof typeof text]),
    getTranslateByKey: ({ key }: { key: string }) =>
      of(text[key as keyof typeof text]),
  };

  return makeEnvironmentProviders([
    {
      provide: I18NService,
      useValue: i18nLocal,
    },
  ]);
}
