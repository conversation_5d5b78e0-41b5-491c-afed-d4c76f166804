import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed } from '@angular/core/testing';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, take, throwError } from 'rxjs';
import { GetRaffleUsecase } from '../../../../../app/modules/contest-entries/application/usecases/get-raffle.usecase';
import {
  RaffleTicketsRequest,
  RaffleTicketsResponse,
  RaffleTicketsUI,
} from '../../../../../app/modules/contest-entries/domain/raffle';
import { RaffleRepository } from '../../../../../app/modules/contest-entries/domain/repositories/raffle.repository';

const setup = () => {
  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: RaffleRepository,
        useValue: {
          getRaffle: (request: RaffleTicketsRequest) => {
            // Default mock behavior
            return throwError(() => new HttpErrorResponse({ status: 500 }));
          },
        },
      },
      GetRaffleUsecase,
    ],
  });

  const usecase = TestBed.inject(GetRaffleUsecase);
  const repository = TestBed.inject(RaffleRepository);
  const loader = TestBed.inject(LoaderService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const showLoaderSpy = spyOn(loader, 'show').and.callThrough();
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.callThrough();

  return {
    usecase,
    repository,
    loader,
    errorHandler,
    showLoaderSpy,
    hideLoaderSpy,
    spyErrorHandler,
  };
};

describe('GetRaffleUsecase', () => {
  it('should be created', () => {
    const { usecase } = setup();
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetRaffleUsecase);
  });

  it('should throw an error if participant code is null', fakeAsync(() => {
    const { usecase, showLoaderSpy, hideLoaderSpy, spyErrorHandler } = setup();

    let result: unknown;
    usecase
      .execute({ participantId: null as any, page: 0 })
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: (e: unknown) => {
          result = e;
        },
      });

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect((result as RuntimeMerchantError).message).toBe(
      'El código del participante es requerido'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  }));

  it('should throw an error if page is invalid', fakeAsync(() => {
    const { usecase, showLoaderSpy, hideLoaderSpy, spyErrorHandler } = setup();

    let result: unknown;
    usecase
      .execute({ participantId: 'any-id', page: -1 })
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: (e: unknown) => {
          result = e;
        },
      });

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect((result as RuntimeMerchantError).message).toBe(
      'La página debe ser mayor a 0'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledTimes(1);
  }));

  it('should handle participant not found error from repository', fakeAsync(() => {
    const {
      repository,
      usecase,
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
    } = setup();
    const participantCode = 'nonexistent-code';
    const errorResponse = new HttpErrorResponse({
      status: 404,
      error: { code: 'APZRWS009' },
    });

    spyOn(repository, 'getRaffle').and.returnValue(
      throwError(() => errorResponse)
    );

    let result: unknown;
    usecase
      .execute({ participantId: participantCode, page: 0 })
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: (e: unknown) => {
          result = e;
        },
      });

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect((result as RuntimeMerchantError).message).toBe(
      'El código del participante no existe'
    );
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).not.toHaveBeenCalled();
  }));

  it('should handle generic errors correctly', fakeAsync(() => {
    const {
      repository,
      usecase,
      showLoaderSpy,
      hideLoaderSpy,
      spyErrorHandler,
    } = setup();
    const err = new HttpErrorResponse({
      status: 500,
      statusText: 'Server Error',
    });
    spyOn(repository, 'getRaffle').and.returnValue(throwError(() => err));

    usecase
      .execute({ participantId: 'any-code', page: 0 })
      .pipe(take(1))
      .subscribe({
        next: fail,
        error: () => {
          // nothing to do here
        },
      });

    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(spyErrorHandler).toHaveBeenCalledWith(err);
  }));

  it('should fetch raffle details successfully', fakeAsync(() => {
    const { repository, usecase, showLoaderSpy, hideLoaderSpy } = setup();
    const mockRaffleData: RaffleTicketsResponse = {
      content: [
        {
          id: 1,
          participantName: 'Test Participant',
          participantId: 'valid-code',
          ticketNumber: 'T-123',
          assignationType: 'PURCHASE',
          campaignId: 'C-456',
          createdAt: new Date().toISOString(),
        },
      ],
      totalElements: 1,
      totalPages: 1,
      size: 1,
      number: 0,
      numberOfElements: 1,
      first: true,
      last: true,
    };
    spyOn(repository, 'getRaffle').and.returnValue(of(mockRaffleData));

    let result: RaffleTicketsUI | undefined;
    usecase
      .execute({ participantId: 'valid-code', page: 0 })
      .pipe(take(1))
      .subscribe({
        next: (data: RaffleTicketsUI) => {
          result = data;
        },
        error: fail,
      });

    expect(result?.data.length).toBe(1);
    expect(result?.participantId).toBe('valid-code');
    expect(result?.totalItems).toBe(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
  }));
});
