/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { AplazoIconRegistryService } from '@aplazo/shared-ui/icon';
import { StatsigService } from '@statsig/angular-bindings';
import { BehaviorSubject, of, throwError } from 'rxjs';
import { GetRaffleUsecase } from '../../../../../app/modules/contest-entries/application/usecases/get-raffle.usecase';
import { RaffleTicketsUI } from '../../../../../app/modules/contest-entries/domain/raffle';
import { RaffleCampaignComponent } from '../../../../../app/modules/contest-entries/infra/pages/raffle-campaign/raffle-campaign.component';
import { RaffleStoreService } from '../../../../../app/modules/contest-entries/infra/services/raffle-store.service';
import { DynamicErrorService } from '../../../../../app/modules/shared/participant-code/dynamic-error.service';

const mockRaffleTicketsUI: RaffleTicketsUI = {
  data: [
    {
      id: 1,
      participantName: 'Test Participant',
      participantId: 'valid-code',
      ticketNumber: 'T-123',
      assignationType: 'PURCHASE',
      campaignId: 'C-456',
      createdAt: new Date().toISOString(),
    },
  ],
  participantId: 'valid-code',
  participantName: 'Test Participant',
  totalItems: 1,
  totalPages: 1,
};

describe('RaffleCampaignComponent', () => {
  let component: RaffleCampaignComponent;
  let fixture: ComponentFixture<RaffleCampaignComponent>;
  let getRaffleUsecase: jasmine.SpyObj<GetRaffleUsecase>;
  let store: RaffleStoreService;
  let iconRegister: jasmine.SpyObj<AplazoIconRegistryService>;
  let statsigService: StatsigService;
  let logEventSpy: jasmine.Spy;
  let dynamicErrorService: DynamicErrorService;
  let storeSpy: jasmine.SpyObj<RaffleStoreService>;

  beforeEach(() => {
    const getRaffleUsecaseSpy = jasmine.createSpyObj('GetRaffleUsecase', [
      'execute',
    ]);
    const iconRegisterSpy = jasmine.createSpyObj('AplazoIconRegistryService', [
      'registerIcons',
    ]);
    storeSpy = jasmine.createSpyObj('RaffleStoreService', [
      'setNewRaffle',
      'clearRaffle',
    ]);

    TestBed.configureTestingModule({
      imports: [RaffleCampaignComponent],
      providers: [
        { provide: GetRaffleUsecase, useValue: getRaffleUsecaseSpy },
        {
          provide: RaffleStoreService,
          useValue: {
            ...storeSpy,
            raffle$: new BehaviorSubject(mockRaffleTicketsUI),
            hasDetails$: new BehaviorSubject(true),
            clearRaffle: jasmine.createSpy('clearRaffle'),
            setNewRaffle: jasmine.createSpy('setNewRaffle'),
          },
        },
        { provide: AplazoIconRegistryService, useValue: iconRegisterSpy },
        {
          provide: StatsigService,
          useValue: {
            logEvent: () => {
              void 0;
            },
          },
        },
        DynamicErrorService,
      ],
    });

    fixture = TestBed.createComponent(RaffleCampaignComponent);
    component = fixture.componentInstance;

    getRaffleUsecase = TestBed.inject(
      GetRaffleUsecase
    ) as jasmine.SpyObj<GetRaffleUsecase>;
    store = TestBed.inject(RaffleStoreService);
    iconRegister = TestBed.inject(
      AplazoIconRegistryService
    ) as jasmine.SpyObj<AplazoIconRegistryService>;
    statsigService = TestBed.inject(StatsigService);
    dynamicErrorService = TestBed.inject(DynamicErrorService);

    logEventSpy = spyOn(statsigService, 'logEvent').and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should register icons on construction', () => {
    expect(iconRegister.registerIcons).toHaveBeenCalled();
  });

  it('should clear search and store on clear()', () => {
    component.search.set('some value');
    component.clear();
    expect(component.search()).toBeNull();
    expect(store.clearRaffle).toHaveBeenCalled();
  });

  it('should call clear on ngOnDestroy', () => {
    spyOn(component, 'clear');
    component.ngOnDestroy();
    expect(component.clear).toHaveBeenCalled();
  });

  it('should change page and retrieve participant info', () => {
    const retrieveSpy = spyOn(component, 'retrieveParticipantInfo');
    component.changePage(2);
    expect(component.selectedPage()).toBe(2);
    expect(retrieveSpy).toHaveBeenCalled();
  });

  describe('retrieveParticipantInfo', () => {
    it('should not call use case if search is not set', async () => {
      component.search.set(null);
      await component.retrieveParticipantInfo();
      expect(getRaffleUsecase.execute).not.toHaveBeenCalled();
    });

    it('should call use case and set raffle on success', fakeAsync(() => {
      const searchValue = 'test1234';
      component.search.set(searchValue);
      getRaffleUsecase.execute.and.returnValue(of(mockRaffleTicketsUI));

      component.retrieveParticipantInfo();
      tick();

      expect(getRaffleUsecase.execute).toHaveBeenCalledWith({
        participantId: searchValue,
        page: 0,
      });
      expect(store.setNewRaffle).toHaveBeenCalledWith(mockRaffleTicketsUI);
      expect(logEventSpy).toHaveBeenCalledWith(
        'lpa_front_get_raffle_success',
        searchValue,
        {
          search_term: searchValue,
          participant_id: mockRaffleTicketsUI.participantId ?? '',
        }
      );
    }));

    it('should handle controlled error for participant not found', fakeAsync(() => {
      const searchValue = 'notfound';
      const errorResponse = new RuntimeMerchantError(
        'Participant not found',
        'GetRaffleUsecase::participantNotFound'
      );
      (errorResponse as any).status_code = 404;
      getRaffleUsecase.execute.and.returnValue(throwError(() => errorResponse));
      const updateSpy = spyOn(
        dynamicErrorService.dynamicErrors,
        'update'
      ).and.callThrough();

      component.search.set(searchValue);
      component.retrieveParticipantInfo();
      tick();

      expect(getRaffleUsecase.execute).toHaveBeenCalledWith({
        participantId: searchValue,
        page: 0,
      });
      expect(store.setNewRaffle).not.toHaveBeenCalled();
      expect(updateSpy).toHaveBeenCalled();
      expect(logEventSpy).toHaveBeenCalledWith(
        'lpa_front_get_raffle_error',
        searchValue,
        {
          search_term: searchValue,
          error: errorResponse.message ?? '',
          error_code: errorResponse.code ?? '',
          error_name:
            (errorResponse as any).status_code ?? errorResponse.name ?? '',
        }
      );
    }));

    it('should handle other controlled errors without updating dynamic errors', fakeAsync(() => {
      const searchValue = 'othererror';
      const errorResponse = new RuntimeMerchantError(
        'Another error',
        'SomeOther::ErrorCode'
      );
      getRaffleUsecase.execute.and.returnValue(throwError(() => errorResponse));
      const updateSpy = spyOn(dynamicErrorService.dynamicErrors, 'update');

      component.search.set(searchValue);
      component.retrieveParticipantInfo();
      tick();

      expect(getRaffleUsecase.execute).toHaveBeenCalledWith({
        participantId: searchValue,
        page: 0,
      });
      expect(store.setNewRaffle).not.toHaveBeenCalled();
      expect(updateSpy).not.toHaveBeenCalled();
      expect(logEventSpy).toHaveBeenCalledWith(
        'lpa_front_get_raffle_error',
        searchValue,
        {
          search_term: searchValue,
          error: errorResponse.message ?? '',
          error_code: errorResponse.code ?? '',
          error_name: errorResponse.name ?? '',
        }
      );
    }));

    it('should not log to console for a use case error', fakeAsync(() => {
      const searchValue = 'unexpected';
      const error = new Error('Unexpected error');
      const consoleWarnSpy = spyOn(console, 'warn');
      getRaffleUsecase.execute.and.returnValue(throwError(() => error));

      component.search.set(searchValue);
      component.retrieveParticipantInfo();
      tick();

      expect(consoleWarnSpy).not.toHaveBeenCalled();
    }));

    it('should handle unexpected errors by logging to console', fakeAsync(() => {
      const searchValue = 'unexpected';
      spyOn(console, 'warn');

      const errorInSuccess = new Error('Error in success logic');
      getRaffleUsecase.execute.and.returnValue(of(mockRaffleTicketsUI));
      (store.setNewRaffle as jasmine.Spy).and.throwError(errorInSuccess);

      component.search.set(searchValue);
      component.retrieveParticipantInfo();
      tick();

      expect(console.warn).toHaveBeenCalledWith(errorInSuccess);
    }));
  });
});
