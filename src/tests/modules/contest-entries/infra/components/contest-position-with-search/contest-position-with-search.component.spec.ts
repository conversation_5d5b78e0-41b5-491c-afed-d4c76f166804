/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  ComponentFixture,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { AplazoIconRegistryService } from '@aplazo/shared-ui/icon';
import { StatsigService } from '@statsig/angular-bindings';
import { of, throwError } from 'rxjs';
import { AnalyticsService } from '../../../../../../app/core/application/services/analytics.service';
import { GetOneParticipantWithDetailsUseCase } from '../../../../../../app/modules/contest-entries/application/usecases/get-one-with-details.usecase';
import {
  ContestRankingUI,
  ParticipantRankUI,
} from '../../../../../../app/modules/contest-entries/domain/contest';
import { AplazoContestPositionWithSearchComponent } from '../../../../../../app/modules/contest-entries/infra/components/contest-position-with-search/contest-position-with-search.component';
import { ContestStoreService } from '../../../../../../app/modules/contest-entries/infra/services/contest-store.service';
import { DynamicErrorService } from '../../../../../../app/modules/shared/participant-code/dynamic-error.service';

describe('AplazoContestPositionWithSearchComponent', () => {
  let component: AplazoContestPositionWithSearchComponent;
  let fixture: ComponentFixture<AplazoContestPositionWithSearchComponent>;
  let getOneUseCase: jasmine.SpyObj<GetOneParticipantWithDetailsUseCase>;
  let store: jasmine.SpyObj<ContestStoreService>;
  let analytics: jasmine.SpyObj<AnalyticsService>;
  let iconRegister: jasmine.SpyObj<AplazoIconRegistryService>;
  let logEventSpy: jasmine.Spy;
  let dynamicErrorService: DynamicErrorService;

  beforeEach(() => {
    const getOneUseCaseSpy = jasmine.createSpyObj(
      'GetOneParticipantWithDetailsUseCase',
      ['execute']
    );
    const storeSpy = jasmine.createSpyObj('ContestStoreService', [
      'setNewRanking',
      'clearRanking',
      'hasDetails$',
    ]);
    const analyticsSpy = jasmine.createSpyObj('AnalyticsService', ['track']);
    const iconRegisterSpy = jasmine.createSpyObj('AplazoIconRegistryService', [
      'registerIcons',
    ]);

    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, AplazoContestPositionWithSearchComponent],
      declarations: [],
      providers: [
        {
          provide: GetOneParticipantWithDetailsUseCase,
          useValue: getOneUseCaseSpy,
        },
        { provide: ContestStoreService, useValue: storeSpy },
        { provide: AnalyticsService, useValue: analyticsSpy },
        { provide: AplazoIconRegistryService, useValue: iconRegisterSpy },
        {
          provide: StatsigService,
          useValue: {
            logEvent: () => {
              void 0;
            },
          },
        },
      ],
    });

    fixture = TestBed.createComponent(AplazoContestPositionWithSearchComponent);
    component = fixture.componentInstance;

    getOneUseCase = TestBed.inject(
      GetOneParticipantWithDetailsUseCase
    ) as jasmine.SpyObj<GetOneParticipantWithDetailsUseCase>;
    store = TestBed.inject(
      ContestStoreService
    ) as jasmine.SpyObj<ContestStoreService>;
    analytics = TestBed.inject(
      AnalyticsService
    ) as jasmine.SpyObj<AnalyticsService>;
    iconRegister = TestBed.inject(
      AplazoIconRegistryService
    ) as jasmine.SpyObj<AplazoIconRegistryService>;
    dynamicErrorService = TestBed.inject(DynamicErrorService);

    const statsigService = TestBed.inject(StatsigService);
    logEventSpy = spyOn(statsigService, 'logEvent').and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should clear search and store on clear()', () => {
    component.search.set('some value');
    component.clear();
    expect(component.search()).toBeNull();
    expect(store.clearRanking).toHaveBeenCalled();
  });

  it('should call use case on retrieveParticipantInfo', fakeAsync(() => {
    const searchValue = 'test';
    const participantDetailsMock: ContestRankingUI = {
      data: {} as ParticipantRankUI[],
      campaignFinishDate: null,
      isFutureDate: true,
      qrUrl: 'https://example.com/qr',
      hasDetails: true,
    };
    component.search.set(searchValue);
    getOneUseCase.execute.and.returnValue(of(participantDetailsMock));

    component.retrieveParticipantInfo();
    tick();

    expect(getOneUseCase.execute).toHaveBeenCalledWith(searchValue);
  }));

  it('should handle async validation error', fakeAsync(() => {
    const searchValue = 'test';
    const errorResponse = new RuntimeMerchantError(
      'El código del participante no existe',
      'GetOneWithDetailsUseCase::participantNotFound'
    );
    getOneUseCase.execute.and.returnValue(throwError(() => errorResponse));
    const updateSpy = spyOn(dynamicErrorService.dynamicErrors, 'update');

    component.search.set(searchValue);
    component.retrieveParticipantInfo();
    tick();

    expect(updateSpy).toHaveBeenCalled();
  }));

  it('should log event on retrieveParticipantInfo with success usecase response', fakeAsync(() => {
    const searchValue = 'test';
    const participantDetailsMock: ContestRankingUI = {
      data: [
        {
          totalRegistration: 100,
        },
      ] as ParticipantRankUI[],
      campaignFinishDate: '',
      isFutureDate: true,
      qrUrl: 'https://example.com/qr',
      hasDetails: true,
      participantId: '123',
      tier: 1,
    };
    getOneUseCase.execute.and.returnValue(of(participantDetailsMock));

    component.search.set(searchValue);
    component.retrieveParticipantInfo();
    tick();

    expect(logEventSpy).toHaveBeenCalledWith(
      'lpa_front_get_ranking_success',
      searchValue,
      {
        search_term: searchValue,
        finish_date: participantDetailsMock.campaignFinishDate,
        participant_id: participantDetailsMock.participantId,
        tier: participantDetailsMock.tier?.toString(),
        total_registration:
          participantDetailsMock.data[0].totalRegistration.toString(),
      }
    );
  }));
});
