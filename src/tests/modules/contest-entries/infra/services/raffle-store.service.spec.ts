import { TestBed } from '@angular/core/testing';
import { firstValueFrom } from 'rxjs';
import { RaffleTicketsUI } from '../../../../../app/modules/contest-entries/domain/raffle';
import {
  defaultRaffle,
  RaffleStoreService,
} from '../../../../../app/modules/contest-entries/infra/services/raffle-store.service';

describe('RaffleStoreService', () => {
  let service: RaffleStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [RaffleStoreService],
    });
    service = TestBed.inject(RaffleStoreService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have default raffle value initially', async () => {
    const raffle = await firstValueFrom(service.raffle$);
    const hasDetails = await firstValueFrom(service.hasDetails$);

    expect(raffle).toEqual(defaultRaffle);
    expect(hasDetails).toBeFalse();
  });

  it('should set new raffle and update hasDetails', async () => {
    const newRaffle: RaffleTicketsUI = {
      data: [
        {
          id: 1,
          participantName: 'Test',
          participantId: '123',
          ticketNumber: 'T1',
          assignationType: 'A',
          campaignId: 'C1',
          createdAt: '',
        },
      ],
      participantId: '123',
      totalItems: 1,
      totalPages: 1,
    };

    service.setNewRaffle(newRaffle);
    const raffle = await firstValueFrom(service.raffle$);
    const hasDetails = await firstValueFrom(service.hasDetails$);

    expect(raffle).toEqual(newRaffle);
    expect(hasDetails).toBeTrue();
  });

  it('should clear raffle and reset hasDetails', async () => {
    const newRaffle: RaffleTicketsUI = {
      data: [
        {
          id: 1,
          participantName: 'Test',
          participantId: '123',
          ticketNumber: 'T1',
          assignationType: 'A',
          campaignId: 'C1',
          createdAt: '',
        },
      ],
      participantId: '123',
      totalItems: 1,
      totalPages: 1,
    };

    service.setNewRaffle(newRaffle);
    service.clearRaffle();
    const raffle = await firstValueFrom(service.raffle$);
    const hasDetails = await firstValueFrom(service.hasDetails$);

    expect(raffle).toEqual(defaultRaffle);
    expect(hasDetails).toBeFalse();
  });
});
