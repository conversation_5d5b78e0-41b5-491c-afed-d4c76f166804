import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { POS_ENVIRONMENT_CORE } from 'src/app-core/domain/environments';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';
import { RaffleRepository } from '../../../../../app/modules/contest-entries/domain/repositories/raffle.repository';
import {
  provideContest,
  provideRaffle,
} from '../../../../../app/modules/contest-entries/infra/config/providers';
import { ContestWithHttpRepository } from '../../../../../app/modules/contest-entries/infra/repositories/contest-with-http.repository';
import { RaffleWithHttpRepository } from '../../../../../app/modules/contest-entries/infra/repositories/raffle-with-http.repository';

describe('providers for contest module', () => {
  describe('provideContest', () => {
    beforeEach(() => {
      TestBed.configureTestingModule({
        providers: [
          provideContest(),
          provideHttpClientTesting(),
          provideHttpClient(),
          {
            provide: POS_ENVIRONMENT_CORE,
            useValue: {
              apiUrl: 'https://api.example.com',
            },
          },
        ],
      });
    });

    it('should provide the correct providers', () => {
      const contestRegions = TestBed.inject(ContestRepository);

      expect(contestRegions).toBeDefined();
      expect(contestRegions).toBeInstanceOf(ContestWithHttpRepository);
    });
  });

  describe('provideRaffle', () => {
    beforeEach(() => {
      TestBed.configureTestingModule({
        providers: [
          provideRaffle(),
          provideHttpClientTesting(),
          provideHttpClient(),
          {
            provide: POS_ENVIRONMENT_CORE,
            useValue: {
              apiUrl: 'https://api.example.com',
            },
          },
        ],
      });
    });

    it('should provide the correct providers', () => {
      const raffle = TestBed.inject(RaffleRepository);

      expect(raffle).toBeDefined();
      expect(raffle).toBeInstanceOf(RaffleWithHttpRepository);
    });
  });
});
