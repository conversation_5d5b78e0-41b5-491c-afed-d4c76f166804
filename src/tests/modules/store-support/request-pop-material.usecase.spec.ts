import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { BehaviorSubject, EMPTY, of, throwError } from 'rxjs';
import { StoreService } from 'src/app/core/application/services/store.service';
import { Branch } from 'src/app/core/domain/entities';
import {
  PopMaterialUIRequest,
  RequestPopMaterialUsecase,
} from 'src/app/modules/store-support/application/usecases/request-pop-material.usecase';

import { StorefrontHelpWithHttpRepository } from 'src/app/modules/store-support/infra/repositories/storefront-help-with-http.repository';

const mockRequest: PopMaterialUIRequest = {
  name: 'Test Name',
  phone: '1234567890',
  comments: 'Test comment',
  hasTentCard: true,
};

const mockBranch: Partial<Branch> = {
  id: 123,
  name: 'Test Branch',
};

const setup = () => {
  const repositorySpy = jasmine.createSpyObj<StorefrontHelpWithHttpRepository>(
    'StorefrontHelpWithHttpRepository',
    ['requestPopMaterial']
  );

  const selectedBranch$ = new BehaviorSubject<Branch | null>(
    mockBranch as Branch
  );
  const merchantId$ = new BehaviorSubject<string | null>('merchant-456');

  TestBed.configureTestingModule({
    providers: [
      RequestPopMaterialUsecase,
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: StorefrontHelpWithHttpRepository,
        useValue: repositorySpy,
      },
      {
        provide: StoreService,
        useValue: {
          getMerchantId$: () => merchantId$.asObservable(),
          selectedBranch$: selectedBranch$.asObservable(),
        },
      },
    ],
  });

  const usecase = TestBed.inject(RequestPopMaterialUsecase);
  const repository = TestBed.inject(
    StorefrontHelpWithHttpRepository
  ) as jasmine.SpyObj<StorefrontHelpWithHttpRepository>;
  const loader = TestBed.inject(LoaderService);
  const notifier = TestBed.inject(NotifierService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const showLoaderSpy = spyOn(loader, 'show').and.returnValue('test-loader-id');
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  const successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.returnValue(EMPTY);

  return {
    usecase,
    repository,
    showLoaderSpy,
    hideLoaderSpy,
    successNotifierSpy,
    spyErrorHandler,
    merchantId$,
    selectedBranch$,
  };
};

describe('RequestPopMaterialUsecase', () => {
  it('should be created', () => {
    const { usecase } = setup();
    expect(usecase).toBeTruthy();
  });

  it('should show and hide loader on successful execution', fakeAsync(() => {
    const { usecase, repository, showLoaderSpy, hideLoaderSpy } = setup();
    repository.requestPopMaterial.and.returnValue(of(undefined));

    usecase.execute(mockRequest).subscribe();
    tick();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith('test-loader-id');
  }));

  it('should show and hide loader on repository error', fakeAsync(() => {
    const { usecase, repository, showLoaderSpy, hideLoaderSpy } = setup();
    repository.requestPopMaterial.and.returnValue(
      throwError(() => new Error('Repo Error'))
    );

    usecase.execute(mockRequest).subscribe();
    tick();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith('test-loader-id');
  }));

  it('should handle error when name is missing', fakeAsync(() => {
    const { usecase, spyErrorHandler, hideLoaderSpy } = setup();
    const request: PopMaterialUIRequest = { ...mockRequest, name: null as any };

    usecase.execute(request).subscribe();
    tick();

    expect(spyErrorHandler).toHaveBeenCalledWith(
      jasmine.any(RuntimeMerchantError),
      jasmine.any(Object)
    );
    const error = spyErrorHandler.calls.mostRecent()
      .args[0] as RuntimeMerchantError;
    expect(error).toBeInstanceOf(RuntimeMerchantError);
    expect(error.message).toBe('El nombre es requerido');
    expect(hideLoaderSpy).toHaveBeenCalledWith('test-loader-id');
  }));

  it('should handle error when phone is missing', fakeAsync(() => {
    const { usecase, spyErrorHandler, hideLoaderSpy } = setup();
    const request: PopMaterialUIRequest = {
      ...mockRequest,
      phone: null as any,
    };

    usecase.execute(request).subscribe();
    tick();

    expect(spyErrorHandler).toHaveBeenCalledWith(
      jasmine.any(RuntimeMerchantError),
      jasmine.any(Object)
    );
    const error = spyErrorHandler.calls.mostRecent()
      .args[0] as RuntimeMerchantError;
    expect(error).toBeInstanceOf(RuntimeMerchantError);
    expect(error.message).toBe('El teléfono es requerido');
    expect(hideLoaderSpy).toHaveBeenCalledWith('test-loader-id');
  }));

  it('should handle error when phone is invalid', fakeAsync(() => {
    const { usecase, spyErrorHandler, hideLoaderSpy } = setup();
    const request: PopMaterialUIRequest = {
      ...mockRequest,
      phone: 'invalid-phone',
    };

    usecase.execute(request).subscribe();
    tick();

    expect(spyErrorHandler).toHaveBeenCalled();
    const error = spyErrorHandler.calls.mostRecent()
      .args[0] as RuntimeMerchantError;
    expect(error).toBeInstanceOf(RuntimeMerchantError);
    expect(error.message).toBe('El teléfono no es válido');
    expect(hideLoaderSpy).toHaveBeenCalledWith('test-loader-id');
  }));

  it('should call repository with correct data and show success message', fakeAsync(() => {
    const { usecase, repository, successNotifierSpy } = setup();
    repository.requestPopMaterial.and.returnValue(of(undefined));

    usecase.execute(mockRequest).subscribe();
    tick();

    expect(repository.requestPopMaterial).toHaveBeenCalledWith({
      name: mockRequest.name,
      phone: mockRequest.phone,
      merchantId: 'merchant-456',
      storefrontId: '123',
      comments: mockRequest.comments,
      hasTentCard: mockRequest.hasTentCard,
    });

    expect(successNotifierSpy).toHaveBeenCalledWith({
      title: 'Hemos recibido tu solicitud.',
      message:
        'Pronto nos pondremos en contacto contigo para coordinar la entrega del material.',
    });
  }));

  it('should call repository with empty strings for merchantId and storefrontId if they are null', fakeAsync(() => {
    const { usecase, repository, merchantId$, selectedBranch$ } = setup();
    repository.requestPopMaterial.and.returnValue(of(undefined));
    merchantId$.next(null);
    selectedBranch$.next(null);

    usecase.execute(mockRequest).subscribe();
    tick();

    expect(repository.requestPopMaterial).toHaveBeenCalledWith({
      name: mockRequest.name,
      phone: mockRequest.phone,
      merchantId: '',
      storefrontId: '',
      comments: mockRequest.comments,
      hasTentCard: mockRequest.hasTentCard,
    });
  }));

  it('should handle repository error through the error handler', fakeAsync(() => {
    const { usecase, repository, spyErrorHandler, successNotifierSpy } =
      setup();
    const error = new Error('Repository Error');
    repository.requestPopMaterial.and.returnValue(throwError(() => error));

    usecase.execute(mockRequest).subscribe();
    tick();

    expect(spyErrorHandler).toHaveBeenCalledWith(error);
    expect(successNotifierSpy).not.toHaveBeenCalled();
  }));
});
