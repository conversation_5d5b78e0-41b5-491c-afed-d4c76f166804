import { TestBed } from '@angular/core/testing';
import { NotifierService } from '@aplazo/merchant/shared';
import { provideNotifierTesting } from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { TrainingFormComponent } from '../../../app/modules/store-support/training-form.component';
import { RequestTrainingUsecase } from '../../../app/modules/storefront-help/request-training.usecase';
import { provideI18NTesting } from '../../i18n.local';

describe('TrainingFormComponent', () => {
  let component: TrainingFormComponent;
  let trainingUsecaseSpy: jasmine.SpyObj<RequestTrainingUsecase>;
  let warnNotifySpy: jasmine.Spy;

  beforeEach(() => {
    trainingUsecaseSpy = jasmine.createSpyObj('RequestTrainingUsecase', [
      'execute',
    ]);

    TestBed.configureTestingModule({
      imports: [TrainingFormComponent],
      providers: [
        {
          provide: RequestTrainingUsecase,
          useValue: trainingUsecaseSpy,
        },
        provideNotifierTesting(),
        provideI18NTesting('storefront-help'),
      ],
    });

    component = TestBed.createComponent(
      TrainingFormComponent
    ).componentInstance;

    const notifier = TestBed.inject(NotifierService);
    warnNotifySpy = spyOn(notifier, 'warning').and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call notifier when form is invalid', async () => {
    const mockForm = {
      form: {
        markAllAsTouched: jasmine.createSpy(),
      },
      invalid: true,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    await component.requestTraining(mockForm as any);

    expect(warnNotifySpy).toHaveBeenCalled();
  });

  it('should call usecase and clear form on valid submission', async () => {
    trainingUsecaseSpy.execute.and.returnValue(of(void 0));

    component.trainingType.set('conocimientos-generales');
    component.contactName.set('Test Name');
    component.contactPhone.set('1234567890');
    component.additionalComments.set('Test comments');

    const mockForm = {
      form: {
        markAllAsTouched: jasmine.createSpy(),
        reset: jasmine.createSpy(),
      },
      invalid: false,
    };

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    await component.requestTraining(mockForm as any);

    expect(trainingUsecaseSpy.execute).toHaveBeenCalledWith({
      trainingType: 'conocimientos-generales',
      contactName: 'Test Name',
      contactPhone: '1234567890',
      additionalComments: 'Test comments',
    });
  });

  it('should update signals on input change', () => {
    component.trainingType.set('practicos-cobro');
    expect(component.trainingType()).toBe('practicos-cobro');

    component.contactName.set('New Name');
    expect(component.contactName()).toBe('New Name');

    component.contactPhone.set('0987654321');
    expect(component.contactPhone()).toBe('0987654321');

    component.additionalComments.set('These are comments');
    expect(component.additionalComments()).toBe('These are comments');
  });

  it('should have training types defined', () => {
    expect(component.trainingTypes).toBeDefined();
    expect(component.trainingTypes.length).toBe(3);
    expect(component.trainingTypes[0].value).toBe('conocimientos-generales');
    expect(component.trainingTypes[1].value).toBe('practicos-cobro');
    expect(component.trainingTypes[2].value).toBe('manejo-objeciones');
  });
});
