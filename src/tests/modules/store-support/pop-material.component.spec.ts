import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { NotifierService } from '@aplazo/merchant/shared';
import { provideNotifierTesting } from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { RequestPopMaterialUsecase } from 'src/app/modules/store-support/application/usecases/request-pop-material.usecase';
import { PopMaterialComponent } from 'src/app/modules/store-support/infra/pages/pop-material/pop-material.component';
import { provideI18NTesting } from '../../i18n.local';

describe('PopMaterialComponent', () => {
  let component: PopMaterialComponent;
  let fixture: ComponentFixture<PopMaterialComponent>;
  let popMaterialUsecaseSpy: jasmine.SpyObj<RequestPopMaterialUsecase>;
  let warnNotifySpy: jasmine.Spy;

  beforeEach(() => {
    popMaterialUsecaseSpy = jasmine.createSpyObj('RequestPopMaterialUsecase', [
      'execute',
    ]);

    TestBed.configureTestingModule({
      imports: [PopMaterialComponent, FormsModule],
      providers: [
        {
          provide: RequestPopMaterialUsecase,
          useValue: popMaterialUsecaseSpy,
        },
        provideNotifierTesting(),
        provideI18NTesting('storefront-help'),
      ],
    });

    fixture = TestBed.createComponent(PopMaterialComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    const notifier = TestBed.inject(NotifierService);
    warnNotifySpy = spyOn(notifier, 'warning').and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call notifier when no materials are selected', () => {
    const form = {
      form: {
        markAllAsTouched: jasmine.createSpy(),
      },
      invalid: false,
    } as any;

    component.requestPopMaterial(form);

    expect(warnNotifySpy).toHaveBeenCalled();
  });

  it('should call notifier when form is invalid', () => {
    component.hasTentCard.set(true);
    const form = {
      form: {
        markAllAsTouched: jasmine.createSpy(),
      },
      invalid: true,
    } as any;

    component.requestPopMaterial(form);

    expect(warnNotifySpy).toHaveBeenCalled();
  });

  it('should call usecase and clear form on valid submission', fakeAsync(() => {
    popMaterialUsecaseSpy.execute.and.returnValue(of(void 0));

    const nameInput = fixture.debugElement.query(
      By.css('[data-test="name-input"]')
    );
    const phoneInput = fixture.debugElement.query(
      By.css('[data-test="phone-input"]')
    );
    const tentCardCheckbox = fixture.debugElement.query(
      By.css('[data-test="tent-card-checkbox"]')
    );

    tentCardCheckbox.triggerEventHandler('ngModelChange', true);

    nameInput.nativeElement.value = 'Test Name';
    nameInput.triggerEventHandler('ngModelChange', 'Test Name');

    phoneInput.nativeElement.value = '1234567890';
    phoneInput.triggerEventHandler('ngModelChange', '1234567890');

    fixture.detectChanges();
    tick();

    const submitButton = fixture.debugElement.query(
      By.css('[data-test="submit-button"]')
    );
    submitButton.nativeElement.click();

    fixture.detectChanges();
    tick();

    expect(popMaterialUsecaseSpy.execute).toHaveBeenCalled();
    // expect(
    //   fixture.debugElement.query(By.css('[data-test="name-input"]'))
    //     .nativeElement.value
    // ).toBe('');
    // expect(
    //   fixture.debugElement.query(By.css('[data-test="phone-input"]'))
    //     .nativeElement.value
    // ).toBe('');
    // expect(component.name()).toBeNull();
    // expect(component.phone()).toBeNull();
  }));

  it('should update signals on input change', () => {
    const nameInput = fixture.debugElement.query(
      By.css('[data-test="name-input"]')
    );

    nameInput.nativeElement.value = 'New Name';
    nameInput.triggerEventHandler('ngModelChange', 'New Name');

    expect(component.name()).toBe('New Name');

    const phoneInput = fixture.debugElement.query(
      By.css('[data-test="phone-input"]')
    );

    phoneInput.nativeElement.value = '0987654321';
    phoneInput.triggerEventHandler('ngModelChange', '0987654321');

    expect(component.phone()).toBe('0987654321');

    const commentsInput = fixture.debugElement.query(
      By.css('[data-test="comments-input"]')
    );

    commentsInput.nativeElement.value = 'These are comments';
    commentsInput.triggerEventHandler('ngModelChange', 'These are comments');

    expect(component.comments()).toBe('These are comments');
  });

  it('should update materials computed property', () => {
    expect(component.materials()).toEqual([]);

    component.hasTentCard.set(true);
    fixture.detectChanges();
    expect(component.materials()).toEqual(['Tent Card']);

    component.hasTentCard.set(false);
    fixture.detectChanges();
    expect(component.materials()).toEqual([]);
  });
});
