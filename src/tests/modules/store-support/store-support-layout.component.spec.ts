import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { Component } from '@angular/core';
import { StoreSupportLayoutComponent } from '../../../app/modules/store-support/store-support-layout.component';

@Component({
  template: '<div>Test Child Component</div>',
})
class TestChildComponent {}

describe('StoreSupportLayoutComponent', () => {
  let component: StoreSupportLayoutComponent;
  let fixture: ComponentFixture<StoreSupportLayoutComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        StoreSupportLayoutComponent,
        RouterTestingModule.withRoutes([
          { path: 'test', component: TestChildComponent },
        ]),
      ],
    });

    fixture = TestBed.createComponent(StoreSupportLayoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have correct layout structure', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const layoutDiv = compiled.querySelector('.store-support-layout');

    expect(layoutDiv).toBeTruthy();
  });

  it('should contain router outlet', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const routerOutlet = compiled.querySelector('router-outlet');

    expect(routerOutlet).toBeTruthy();
  });

  it('should apply correct CSS classes to layout container', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const layoutDiv = compiled.querySelector('.store-support-layout');

    expect(layoutDiv).toBeTruthy();
    expect(layoutDiv?.classList.contains('store-support-layout')).toBe(true);
  });
});
