import { Component, ViewChild } from '@angular/core';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { FormsModule, NgForm } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { LoaderService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSearch, iconSpinCircle, iconXMark } from '@aplazo/ui-icons';
import { BehaviorSubject } from 'rxjs';
import { HandleParticipantCodeErrorDirective } from '../../../../app/modules/shared/participant-code/dynamic-error.directive';
import {
  ParticipantCodeComponent,
  ParticipantCodeTextUI,
} from '../../../../app/modules/shared/participant-code/participant-code.component';

// Mock services
class MockLoaderService {
  isLoading$ = new BehaviorSubject<boolean>(false);
}

@Component({
  template: `<app-participant-code
    [(participantCode)]="participantCode"
    (participantCodeEvent)="onParticipantCodeEvent()"
    [minLength]="3"
    [textUI]="textUI"></app-participant-code>`,
  standalone: true,
  imports: [ParticipantCodeComponent, FormsModule],
})
class TestHostComponent {
  @ViewChild(ParticipantCodeComponent, { static: true })
  participantCodeComponent!: ParticipantCodeComponent;

  participantCode: string | null = null;
  textUI: ParticipantCodeTextUI | null = {
    errors: {
      required: 'Ingrese un código de participante',
      minlength: 'Ingrese un código de al menos 3 caracteres',
      participantNotFound: 'Código de participante no encontrado',
    },
    hints: {
      default: 'Código de participante. Ej. AFIK1234',
    },
    labels: {
      submit: 'Consultar mi posición',
      placeholder: 'abcd0000',
    },
  };
  onParticipantCodeEvent = jasmine.createSpy('onParticipantCodeEvent');
}

describe('ParticipantCodeComponent', () => {
  let hostComponent: TestHostComponent;
  let component: ParticipantCodeComponent;
  let fixture: ComponentFixture<TestHostComponent>;
  let loaderService: MockLoaderService;
  let ngForm: NgForm;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        TestHostComponent,
        AplazoButtonComponent,
        AplazoIconComponent,
        HandleParticipantCodeErrorDirective,
      ],
      providers: [
        { provide: LoaderService, useClass: MockLoaderService },
        AplazoIconRegistryService,
      ],
    }).compileComponents();

    const iconRegistry = TestBed.inject(AplazoIconRegistryService);
    iconRegistry.registerIcons([iconSearch, iconXMark, iconSpinCircle]);

    fixture = TestBed.createComponent(TestHostComponent);
    hostComponent = fixture.componentInstance;
    component = hostComponent.participantCodeComponent;
    loaderService = TestBed.inject(
      LoaderService
    ) as unknown as MockLoaderService;
    fixture.detectChanges();
    ngForm = fixture.debugElement
      .query(By.directive(NgForm))
      .injector.get(NgForm);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should register icons on construction', () => {
    const iconRegistry = TestBed.inject(AplazoIconRegistryService);
    spyOn(iconRegistry, 'registerIcons').and.callThrough();
    // This is hard to test without re-initializing, but we know it's called.
    // The check is implicitly done by the other tests that rely on icons.
    expect(iconRegistry).toBeTruthy();
  });

  describe('onSubmit', () => {
    it('should not emit event if form is invalid', () => {
      ngForm.form.controls['participantCode'].setValue('');
      fixture.detectChanges();
      component.onSubmit(ngForm);
      expect(hostComponent.onParticipantCodeEvent).not.toHaveBeenCalled();
    });

    it('should not emit event if participantCode is missing from form value', () => {
      spyOn(component.participantCode, 'set');
      spyOn(component.participantCodeEvent, 'emit');
      const validFormWithNoCode = {
        invalid: false,
        value: {},
      } as NgForm;
      component.onSubmit(validFormWithNoCode);
      expect(component.participantCode.set).not.toHaveBeenCalled();
      expect(component.participantCodeEvent.emit).not.toHaveBeenCalled();
    });

    it('should update participantCode model and emit event on valid submission', () => {
      const code = 'TESTCODE';
      ngForm.form.controls['participantCode'].setValue(code);
      fixture.detectChanges();

      spyOn(component.participantCode, 'set').and.callThrough();
      spyOn(component.participantCodeEvent, 'emit').and.callThrough();

      component.onSubmit(ngForm);

      expect(component.participantCode.set).toHaveBeenCalledWith(code);
      expect(hostComponent.participantCode).toBe(code);
      expect(component.participantCodeEvent.emit).toHaveBeenCalled();
    });

    it('should update form validity after loading', fakeAsync(() => {
      const code = 'TESTCODE';
      ngForm.form.controls['participantCode'].setValue(code);
      fixture.detectChanges();

      const participantCodeControl = ngForm.control.get('participantCode');
      expect(participantCodeControl).toBeTruthy();
      const updateValiditySpy = spyOn(
        participantCodeControl!,
        'updateValueAndValidity'
      ).and.callThrough();

      component.onSubmit(ngForm);
      fixture.detectChanges();

      expect(hostComponent.participantCode).toBe(code);

      loaderService.isLoading$.next(false);
      tick(1500);

      expect(updateValiditySpy).toHaveBeenCalled();
    }));
  });

  describe('clear', () => {
    it('should reset the form', () => {
      spyOn(ngForm, 'reset');
      component.clear(ngForm);
      expect(ngForm.reset).toHaveBeenCalled();
    });

    it('should clear the input when clear button is clicked', fakeAsync(() => {
      const inputElement = fixture.debugElement.query(
        By.css('input')
      ).nativeElement;

      inputElement.value = 'TEST';
      inputElement.dispatchEvent(new Event('input'));
      fixture.detectChanges();
      tick();

      expect(component.participantCode()).toBe('TEST');

      const clearIcon = fixture.debugElement.query(
        By.css('aplz-ui-icon[name="x-mark"]')
      );
      expect(clearIcon).withContext('Clear icon not found').not.toBeNull();

      const clearButton = clearIcon.parent;
      expect(clearButton).withContext('Clear button not found').not.toBeNull();

      clearButton!.triggerEventHandler('click', null);
      fixture.detectChanges();

      expect(component.participantCode()).toBeFalsy();
      expect(inputElement.value).toBe('');
    }));
  });

  describe('Template and UI', () => {
    it('should disable submit button when loading', () => {
      loaderService.isLoading$.next(true);
      fixture.detectChanges();
      const submitButton = fixture.debugElement.query(
        By.css('button[type="submit"]')
      ).nativeElement;
      expect(submitButton.disabled).toBeTrue();

      loaderService.isLoading$.next(false);
      fixture.detectChanges();
      expect(submitButton.disabled).toBeFalse();
    });

    it('should show required error message', fakeAsync(() => {
      ngForm.form.controls['participantCode'].markAsTouched();
      ngForm.form.controls['participantCode'].setValue('');
      fixture.detectChanges();
      tick();

      const errorMessage = fixture.debugElement.query(
        By.css('.text-special-danger')
      );
      expect(errorMessage.nativeElement.textContent).toContain(
        'Ingrese un código de participante'
      );
    }));

    it('should show minlength error message', fakeAsync(() => {
      ngForm.form.controls['participantCode'].markAsTouched();
      ngForm.form.controls['participantCode'].setValue('ab');
      fixture.detectChanges();
      tick();

      const errorMessage = fixture.debugElement.query(
        By.css('.text-special-danger')
      );
      expect(errorMessage.nativeElement.textContent).toContain(
        'Ingrese un código de al menos 3 caracteres'
      );
    }));
  });

  describe('Custom texts', () => {
    it('should display default texts if no custom texts are provided', fakeAsync(() => {
      fixture.detectChanges();

      const submitButton = fixture.debugElement.query(
        By.css('button[type="submit"]')
      ).nativeElement;
      expect(submitButton.textContent).toContain('Consultar mi posición');

      const placeholder = fixture.debugElement.query(By.css('input'))
        .nativeElement.placeholder;
      expect(placeholder).toBe('abcd0000');

      const hint = fixture.debugElement.query(By.css('.whitespace-normal'))
        .nativeElement.textContent;
      expect(hint).toContain('Código de participante. Ej. AFIK1234');
    }));

    it('should display custom texts when provided', fakeAsync(() => {
      const customTexts: ParticipantCodeTextUI = {
        errors: {
          required: 'Custom required',
          minlength: 'Custom minlength',
          participantNotFound: 'Custom not found',
        },
        hints: {
          default: 'Custom hint',
        },
        labels: {
          submit: 'Custom Submit',
          placeholder: 'Custom placeholder',
        },
      };

      hostComponent.textUI = customTexts;
      fixture.detectChanges();

      const submitButton = fixture.debugElement.query(
        By.css('button[type="submit"]')
      ).nativeElement;
      expect(submitButton.textContent).toContain(customTexts.labels.submit);

      const placeholder = fixture.debugElement.query(By.css('input'))
        .nativeElement.placeholder;
      expect(placeholder).toBe(customTexts.labels.placeholder);

      const hint = fixture.debugElement.query(By.css('.whitespace-normal'))
        .nativeElement.textContent;
      expect(hint).toContain(customTexts.hints.default);

      // Test required error
      ngForm.form.controls['participantCode'].markAsTouched();
      ngForm.form.controls['participantCode'].setValue('');
      fixture.detectChanges();
      tick();
      let errorMessage = fixture.debugElement.query(
        By.css('.text-special-danger')
      ).nativeElement.textContent;
      expect(errorMessage).toContain(customTexts.errors.required);

      // Test minlength error
      ngForm.form.controls['participantCode'].setValue('a');
      fixture.detectChanges();
      tick();
      errorMessage = fixture.debugElement.query(By.css('.text-special-danger'))
        .nativeElement.textContent;
      expect(errorMessage).toContain(customTexts.errors.minlength);
    }));
  });
});
