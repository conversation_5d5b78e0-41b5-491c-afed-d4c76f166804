import { TestBed } from '@angular/core/testing';
import { ValidationErrors } from '@angular/forms';
import { DynamicErrorService } from '../../../../app/modules/shared/participant-code/dynamic-error.service';

describe('DynamicErrorService', () => {
  let service: DynamicErrorService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [DynamicErrorService],
    });
    service = TestBed.inject(DynamicErrorService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have an initial empty object for dynamicErrors signal', () => {
    expect(service.dynamicErrors()).toEqual({});
  });

  it('should allow setting new validation errors', () => {
    const newErrors: Record<string, ValidationErrors | null> = {
      testControl: { required: true },
    };
    service.dynamicErrors.set(newErrors);
    expect(service.dynamicErrors()).toEqual(newErrors);
  });

  it('should allow updating validation errors', () => {
    const initialErrors: Record<string, ValidationErrors | null> = {
      control1: { required: true },
    };
    service.dynamicErrors.set(initialErrors);

    service.dynamicErrors.update(errors => ({
      ...errors,
      control2: { min: 5 },
    }));
    expect(service.dynamicErrors()).toEqual({
      control1: { required: true },
      control2: { min: 5 },
    });
  });

  it('should allow removing a validation error by setting it to null', () => {
    const initialErrors: Record<string, ValidationErrors | null> = {
      control1: { required: true },
      control2: { min: 5 },
    };
    service.dynamicErrors.set(initialErrors);

    service.dynamicErrors.update(errors => ({ ...errors, control1: null }));

    expect(service.dynamicErrors()['control1']).toBeNull();
    expect(service.dynamicErrors()['control2']).toEqual({ min: 5 });
  });

  it('should allow removing a validation error property', () => {
    const initialErrors: Record<string, ValidationErrors | null> = {
      control1: { required: true },
      control2: { min: 5 },
    };
    service.dynamicErrors.set(initialErrors);

    service.dynamicErrors.update(errors => {
      const newErrors = { ...errors };
      delete newErrors['control1'];
      return newErrors;
    });

    expect(service.dynamicErrors()['control1']).toBeUndefined();
    expect(service.dynamicErrors()['control2']).toEqual({ min: 5 });
  });
});
