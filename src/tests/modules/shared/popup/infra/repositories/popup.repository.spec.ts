import { TestBed } from '@angular/core/testing';
import { HttpClient } from '@angular/common/http';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { throwError } from 'rxjs';
import {
  PopupRepository,
  PopupMeta,
} from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { POS_ENVIRONMENT_CORE } from '../../../../../../app-core/domain/environments';
import { firstValueFrom } from 'rxjs';
import { PopupEntity } from 'src/app/modules/shared/popup/domain/entities/popup.entity';
import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';

/**
 * NOTA: Mockeamos HttpClient para que siempre falle y así forzar el uso del fallback mock en PopupRepository.
 * Esto es necesario porque el repository ahora intenta hacer llamadas HTTP reales antes de usar el mock.
 */
describe('PopupRepository', () => {
  let repo: PopupRepository;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        PopupRepository,
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            promoApiUrl: 'https://mpromotions.aplazo.net/',
          },
        },
        {
          provide: HttpClient,
          useValue: {
            get: () => throwError(() => new Error('Simulated HTTP error')),
          },
        },
      ],
    });
    repo = TestBed.inject(PopupRepository);
  });

  it('should return dummy popups for any branch', async () => {
    const popups: PopupEntity[] = await firstValueFrom(
      repo.getAvailablePopups('BRANCH-001')
    );
    expect(popups.length).toBe(2);
    expect(popups[0].popupId).toBe('POPUP-001');
  });

  it('should return dummy HTML for POPUP-001', async () => {
    const html = await firstValueFrom(repo.getPopupHtml('POPUP-001'));
    expect(html).toContain('¿Tienes problemas para generar el ticket?');
  });

  it('should return dummy HTML for POPUP-002', async () => {
    const html = await firstValueFrom(repo.getPopupHtml('POPUP-002'));
    expect(html).toContain('¿Sin señal?');
  });

  it('should return not found HTML for unknown popup', async () => {
    const html = await firstValueFrom(repo.getPopupHtml('UNKNOWN'));
    expect(html).toContain('Popup no encontrado');
  });
});
