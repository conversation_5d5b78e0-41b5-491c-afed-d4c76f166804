import { Directive, inject } from '@angular/core';
import {
  AbstractControl,
  AsyncValidator,
  NG_ASYNC_VALIDATORS,
  ValidationErrors,
} from '@angular/forms';
import { DynamicErrorService } from './dynamic-error.service';

@Directive({
  standalone: true,
  selector: '[aplzHandleParticipantCodeError]',
  providers: [
    {
      provide: NG_ASYNC_VALIDATORS,
      useExisting: HandleParticipantCodeErrorDirective,
      multi: true,
    },
  ],
})
export class HandleParticipantCodeErrorDirective implements AsyncValidator {
  readonly #dynamicErrorService = inject(DynamicErrorService);

  async validate(control: AbstractControl): Promise<ValidationErrors | null> {
    const currentCode = control.value;

    const errors =
      this.#dynamicErrorService.dynamicErrors()?.[currentCode] ?? null;

    if (errors) {
      control.setErrors(errors);
    }

    return errors;
  }
}
