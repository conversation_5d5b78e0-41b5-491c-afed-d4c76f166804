<form
  #form="ngForm"
  class="p-8 border-aplazo-aplazo rounded-lg"
  (ngSubmit)="onSubmit(form)">
  <div class="mx-auto max-w-full md:max-w-[280px] grid">
    <div
      class="relative w-full mb-8 flex border-2 rounded-lg"
      [class.border-special-danger]="searchInput.touched && searchInput.errors">
      <button
        aplzButton
        size="sm"
        type="button"
        class="flex-shrink-0 flex-grow-0 bg-light-primary border-e border-e-dark-tertiary pointer-events-none">
        <aplz-ui-icon name="search" size="sm"></aplz-ui-icon>
        <span class="aplazo-component--sr-only">Search</span>
      </button>

      <input
        type="text"
        name="participantCode"
        class="block py-2.5 mx-1 w-full text-base text-dark-secondary ring-0 outline-none border-none rounded-lg"
        inputmode="search"
        [placeholder]="textUI().labels.placeholder"
        [ngModel]="participantCode()"
        (ngModelChange)="participantCode.set($event)"
        required
        [minlength]="minLength()"
        aplazoLettersNumbers
        aplazoTrimSpaces
        aplzHandleParticipantCodeError
        ngModel
        #searchInput="ngModel" />

      @if (participantCode()) {
        <button
          aplzButton
          type="button"
          size="sm"
          class="bg-light-primary border-s border-s-dark-tertiary"
          (click)="clear(form)">
          <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
          <span class="aplazo-component--sr-only">Reset</span>
        </button>
      }

      <div class="absolute top-full text-sm w-full">
        @if (searchInput.touched && searchInput.hasError('required')) {
          <p class="whitespace-normal text-special-danger">
            {{ textUI().errors.required }}
          </p>
        } @else if (searchInput.touched && searchInput.hasError('minlength')) {
          <p class="whitespace-normal text-special-danger">
            {{ textUI().errors.minlength }}
          </p>
        } @else if (
          searchInput.touched && searchInput.hasError(participantErrorKey)
        ) {
          <p class="whitespace-normal text-special-danger">
            {{ searchInput.getError(participantErrorKey) }}
          </p>
        }

        @if (!searchInput.touched || !searchInput.errors) {
          <p class="whitespace-normal">
            {{ textUI().hints.default }}
          </p>
        }
      </div>
    </div>
  </div>

  <button
    aplzButton
    type="submit"
    aplzAppearance="solid"
    aplzColor="dark"
    size="md"
    class="mx-auto"
    [disabled]="isLoading$ | async"
    [rounded]="true">
    @if ((isLoading$ | async) === true) {
      <aplz-ui-icon name="spin-circle" size="sm" [spin]="true"></aplz-ui-icon>
      <span class="ml-2">Consultando...</span>
    } @else {
      {{ textUI().labels.submit }}
    }
  </button>
</form>
