import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  model,
  output,
  ViewEncapsulation,
} from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { LoaderService } from '@aplazo/merchant/shared';
import {
  AplazoLettersNumbersDirective,
  AplazoTrimSpacesDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSearch, iconSpinCircle, iconXMark } from '@aplazo/ui-icons';
import { delay, filter, first } from 'rxjs';
import { HandleParticipantCodeErrorDirective } from './dynamic-error.directive';

export interface ParticipantCodeTextUI {
  errors: {
    required: string;
    minlength: string;
    participantNotFound: string;
  };
  hints: {
    default: string;
  };
  labels: {
    submit: string;
    placeholder: string;
  };
}

export const participantErrorKey = 'participantNotFound';

@Component({
  selector: 'app-participant-code',
  standalone: true,
  templateUrl: './participant-code.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [
    FormsModule,
    AplazoButtonComponent,
    AplazoIconComponent,
    AsyncPipe,
    AplazoLettersNumbersDirective,
    AplazoTrimSpacesDirective,
    HandleParticipantCodeErrorDirective,
  ],
})
export class ParticipantCodeComponent {
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #loader = inject(LoaderService);
  readonly #cdr = inject(ChangeDetectorRef);

  readonly participantErrorKey = participantErrorKey;

  readonly textUI = input<ParticipantCodeTextUI>({
    errors: {
      required: 'Ingrese un código de participante',
      minlength: 'Ingrese un código de al menos 3 caracteres',
      participantNotFound: 'Código de participante no encontrado',
    },
    hints: {
      default: 'Código de participante. Ej. AFIK1234',
    },
    labels: {
      submit: 'Consultar',
      placeholder: 'abcd0000',
    },
  });

  readonly minLength = input<number>(3);

  readonly participantCode = model<string | null>(null);

  readonly participantCodeEvent = output<void>();

  readonly isLoading$ = this.#loader.isLoading$;

  constructor() {
    this.#iconRegister.registerIcons([iconSearch, iconXMark, iconSpinCircle]);
  }

  onSubmit(form: NgForm): void {
    if (form.invalid) {
      return;
    }

    const participantCode = form.value.participantCode;

    if (!participantCode) {
      return;
    }

    this.#loader.isLoading$
      .pipe(
        filter(l => !l),
        first(),
        delay(1500)
      )
      .subscribe(() => {
        form.control.get('participantCode')?.updateValueAndValidity();
        this.#cdr.markForCheck();
      });

    this.participantCode.set(participantCode);
    this.participantCodeEvent.emit();
  }

  clear(form: NgForm): void {
    form.reset();
  }
}
