import { PopupId } from '../value-objects/popup-id.value-object';

/**
 * Popup entity with business logic and validation.
 * Represents a popup that can be displayed to users with business rules.
 */
export class PopupEntity {
  constructor(
    public readonly id: PopupId,
    public readonly title: string,
    public readonly content: string,
    public readonly priority: number,
    public readonly isActive: boolean,
    public readonly createdAt: Date,
    public readonly startAt?: Date,
    public readonly expiresAt?: Date
  ) {}

  /**
   * Gets the popup ID as a string for backward compatibility
   * @returns The popup ID as a string
   */
  get popupId(): string {
    return this.id.getValue();
  }

  /**
   * Validates if the popup is valid for display.
   * @returns True if the popup is valid, false otherwise
   */
  isValid(): boolean {
    return (
      this.title.trim().length > 0 &&
      this.content.trim().length > 0 &&
      this.priority >= 0 &&
      this.isActive
    );
  }

  /**
   * Checks if the popup can be displayed.
   * @returns True if the popup can be displayed, false otherwise
   */
  canBeDisplayed(): boolean {
    return this.isValid() && !this.isExpired() && this.hasStarted();
  }

  /**
   * Checks if the popup has started based on startAt date.
   * @returns True if the popup has started or has no start date, false otherwise
   */
  hasStarted(): boolean {
    if (!this.startAt) {
      return true;
    }
    return new Date() >= this.startAt;
  }

  /**
   * Checks if the popup has expired.
   * @returns True if the popup has expired, false otherwise
   */
  isExpired(): boolean {
    if (!this.expiresAt) {
      return false;
    }
    return new Date() > this.expiresAt;
  }

  /**
   * Gets the time until expiration in milliseconds.
   * @returns Time until expiration in milliseconds, or null if no expiration
   */
  getTimeUntilExpiration(): number | null {
    if (!this.expiresAt) {
      return null;
    }
    const now = new Date();
    const timeUntilExpiration = this.expiresAt.getTime() - now.getTime();
    return timeUntilExpiration > 0 ? timeUntilExpiration : 0;
  }

  /**
   * Creates a new popup with updated title.
   * @param newTitle - The new title for the popup
   * @returns A new PopupEntity instance with the updated title
   */
  withTitle(newTitle: string): PopupEntity {
    return new PopupEntity(
      this.id,
      newTitle,
      this.content,
      this.priority,
      this.isActive,
      this.createdAt,
      this.expiresAt
    );
  }

  /**
   * Creates a new popup with updated content.
   * @param newContent - The new content for the popup
   * @returns A new PopupEntity instance with the updated content
   */
  withContent(newContent: string): PopupEntity {
    return new PopupEntity(
      this.id,
      this.title,
      newContent,
      this.priority,
      this.isActive,
      this.createdAt,
      this.expiresAt
    );
  }

  /**
   * Creates a new popup with updated priority.
   * @param newPriority - The new priority for the popup
   * @returns A new PopupEntity instance with the updated priority
   */
  withPriority(newPriority: number): PopupEntity {
    return new PopupEntity(
      this.id,
      this.title,
      this.content,
      newPriority,
      this.isActive,
      this.createdAt,
      this.expiresAt
    );
  }

  /**
   * Creates a new popup with updated active status.
   * @param isActive - The new active status for the popup
   * @returns A new PopupEntity instance with the updated active status
   */
  withActiveStatus(isActive: boolean): PopupEntity {
    return new PopupEntity(
      this.id,
      this.title,
      this.content,
      this.priority,
      isActive,
      this.createdAt,
      this.expiresAt
    );
  }

  /**
   * Creates a new popup with updated start date.
   * @param startAt - The new start date for the popup
   * @returns A new PopupEntity instance with the updated start date
   */
  withStartDate(startAt: Date): PopupEntity {
    return new PopupEntity(
      this.id,
      this.title,
      this.content,
      this.priority,
      this.isActive,
      this.createdAt,
      startAt,
      this.expiresAt
    );
  }

  /**
   * Creates a new popup with updated expiration date.
   * @param expiresAt - The new expiration date for the popup
   * @returns A new PopupEntity instance with the updated expiration date
   */
  withExpirationDate(expiresAt: Date): PopupEntity {
    return new PopupEntity(
      this.id,
      this.title,
      this.content,
      this.priority,
      this.isActive,
      this.createdAt,
      this.startAt,
      expiresAt
    );
  }

  /**
   * Compares this popup's priority with another popup.
   * @param other - The other popup to compare with
   * @returns Negative if this popup has higher priority, positive if lower, 0 if equal
   */
  comparePriority(other: PopupEntity): number {
    return other.priority - this.priority; // Higher priority numbers come first
  }

  /**
   * Creates a string representation of the popup.
   * @returns A string representation of the popup
   */
  toString(): string {
    return `Popup(id=${this.id.getValue()}, title="${this.title}", priority=${
      this.priority
    })`;
  }
}
