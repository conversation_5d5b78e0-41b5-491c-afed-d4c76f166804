import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { PopupTriggerService } from '../application/services/popup-trigger.service';
import { PopupStorageService } from '../application/services/popup-storage.service';
import { PopupRepository } from '../infra/repositories/popup.repository';
import { StoreService } from '../../../../core/application/services/store.service';
import { of } from 'rxjs';

@Component({
  selector: 'app-popup-demo',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AplazoButtonComponent,
    AplazoCardComponent,
  ],
  styles: [
    `
      .popup-demo-widget {
        position: fixed;
        bottom: 24px;
        right: 24px;
        z-index: 9999;
        max-width: 520px;
        min-width: 420px;
        max-height: 80vh;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
        border-radius: 16px;
        background: white;
        transition: box-shadow 0.2s;
        overflow: auto;
      }
      .popup-demo-toggle-btn {
        position: fixed;
        bottom: 24px;
        right: 24px;
        z-index: 10000;
        border-radius: 50%;
        width: 56px;
        height: 56px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        cursor: pointer;
        border: none;
        outline: none;
        transition: background 0.2s;
      }
      .popup-demo-toggle-btn:hover {
        background: #f3f3f3;
      }
      .popup-demo-close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        background: transparent;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #888;
      }
      .popup-demo-content {
        max-height: calc(80vh - 32px);
        overflow-y: auto;
        padding: 16px;
      }
      .popup-demo-textarea {
        width: 100%;
        min-height: 120px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        resize: vertical;
      }
      .popup-demo-section {
        margin-bottom: 16px;
      }
      .popup-demo-section:last-child {
        margin-bottom: 0;
      }
      .popup-demo-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
      }
      @media (max-width: 768px) {
        .popup-demo-widget {
          max-width: calc(100vw - 48px);
          min-width: 320px;
          right: 12px;
          bottom: 12px;
        }
        .popup-demo-toggle-btn {
          right: 12px;
          bottom: 12px;
        }
        .popup-demo-grid {
          grid-template-columns: 1fr;
        }
      }
    `,
  ],
  template: `
    <button
      *ngIf="!isOpen()"
      class="popup-demo-toggle-btn"
      (click)="isOpen.set(true)">
      🧪
    </button>
    <div *ngIf="isOpen()" class="popup-demo-widget">
      <button
        class="popup-demo-close-btn"
        (click)="isOpen.set(false)"
        title="Cerrar">
        ×
      </button>
      <aplz-ui-card size="full">
        <div class="p-6 space-y-6">
          <h2 class="text-2xl font-bold text-gray-800">🎭 Popup System Demo</h2>

          <!-- Current State -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">📊 Current State</h3>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Branch ID:</strong> {{ currentBranchId() }}</div>
              <div><strong>Today:</strong> {{ today() }}</div>
              <div>
                <strong>Available Popups:</strong>
                {{ availablePopups().length }}
              </div>
              <div><strong>Shown Today:</strong> {{ shownToday() }}</div>
              <div>
                <strong>Dont Show Again:</strong> {{ dontShowAgain().length }}
              </div>
              <div>
                <strong>Eligible:</strong> {{ eligiblePopups().length }}
              </div>
            </div>
          </div>

          <!-- Configuration -->
          <div class="bg-blue-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">⚙️ Configuration</h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">Branch ID:</label>
                <input
                  type="text"
                  [(ngModel)]="config.branchId"
                  class="w-full px-3 py-2 border rounded-md"
                  placeholder="e.g., 36" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Trigger Type:</label
                >
                <select
                  [(ngModel)]="config.triggerType"
                  class="w-full px-3 py-2 border rounded-md">
                  <option value="manual">Manual</option>
                  <option value="login">After Login</option>
                  <option value="payment">After Payment</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Popup Width (px):</label
                >
                <input
                  type="number"
                  [(ngModel)]="config.popupWidth"
                  class="w-full px-3 py-2 border rounded-md"
                  min="300"
                  max="1600"
                  step="10"
                  placeholder="900" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Popup Height (px):</label
                >
                <input
                  type="number"
                  [(ngModel)]="config.popupHeight"
                  class="w-full px-3 py-2 border rounded-md"
                  min="200"
                  max="1200"
                  step="10"
                  placeholder="auto" />
              </div>
            </div>
            <div class="mt-4">
              <label class="block text-sm font-medium mb-1"
                >Custom HTML (solo manual):</label
              >
              <textarea
                [(ngModel)]="config.customHtml"
                rows="4"
                class="w-full px-3 py-2 border rounded-md font-mono text-xs"></textarea>
            </div>
          </div>

          <!-- Actions -->
          <div class="bg-green-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🎯 Actions</h3>
            <div class="flex flex-wrap gap-3">
              <button
                aplzButton
                aplzAppearance="solid"
                aplzColor="aplazo"
                (click)="triggerPopup()"
                [disabled]="isLoading()">
                {{ isLoading() ? 'Loading...' : '🚀 Trigger Popup' }}
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="dark"
                (click)="checkPopupStatus()">
                🔍 Check Status
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="warning"
                (click)="clearTodayRecords()">
                🗑️ Clear Today's Records
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="danger"
                (click)="clearAllRecords()">
                🗑️ Clear All Records
              </button>
            </div>
          </div>

          <!-- Checkbox Testing -->
          <div class="bg-purple-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🔒 Checkbox Testing</h3>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Test Popup ID:</label
                >
                <input
                  type="text"
                  [(ngModel)]="testPopupId"
                  class="w-full px-3 py-2 border rounded-md"
                  placeholder="e.g., popup-1" />
              </div>
              <div class="flex gap-3">
                <button
                  aplzButton
                  aplzAppearance="stroked"
                  aplzColor="warning"
                  (click)="markAsDontShowAgain()">
                  🔒 Mark as "No mostrar de nuevo"
                </button>
                <button
                  aplzButton
                  aplzAppearance="stroked"
                  aplzColor="success"
                  (click)="clearDontShowAgain()">
                  🔓 Clear "No mostrar de nuevo"
                </button>
              </div>
              <div class="text-sm text-gray-600">
                <strong>Status:</strong>
                <span *ngIf="isPopupDontShowAgain()" class="text-red-600"
                  >❌ Marked as "No mostrar de nuevo"</span
                >
                <span *ngIf="!isPopupDontShowAgain()" class="text-green-600"
                  >✅ Can be shown</span
                >
              </div>
            </div>
          </div>

          <!-- Results -->
          <div class="bg-yellow-50 p-4 rounded-lg" *ngIf="lastResult()">
            <h3 class="text-lg font-semibold mb-3">📋 Last Result</h3>
            <pre class="text-sm bg-white p-3 rounded border overflow-auto">{{
              lastResult() | json
            }}</pre>
          </div>

          <!-- Available Popups -->
          <div
            class="bg-purple-50 p-4 rounded-lg"
            *ngIf="availablePopups().length > 0">
            <h3 class="text-lg font-semibold mb-3">📋 Available Popups</h3>
            <div class="space-y-2">
              <div
                *ngFor="let popup of availablePopups()"
                class="bg-white p-3 rounded border">
                <div class="flex justify-between items-center">
                  <div>
                    <strong>{{ popup.popupId }}</strong> (Priority:
                    {{ popup.priority }})
                  </div>
                  <div class="text-sm text-gray-600">
                    {{ popup.startAt }} - {{ popup.endAt }}
                  </div>
                </div>
                <div class="text-xs text-gray-500 mt-1">
                  <span
                    *ngIf="isPopupShownToday(popup.popupId)"
                    class="text-orange-600"
                    >📅 Shown today</span
                  >
                  <span
                    *ngIf="isPopupDontShowAgain(popup.popupId)"
                    class="text-red-600"
                    >🔒 Dont show again</span
                  >
                  <span
                    *ngIf="
                      !isPopupShownToday(popup.popupId) &&
                      !isPopupDontShowAgain(popup.popupId)
                    "
                    class="text-green-600"
                    >✅ Eligible</span
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- Storage Records -->
          <div class="bg-red-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">💾 Storage Records</h3>
            <div class="space-y-2 max-h-40 overflow-auto">
              <div
                *ngFor="let record of storageRecords()"
                class="bg-white p-2 rounded border text-sm">
                <strong>{{ record.popupId }}</strong> - Branch:
                {{ record.branchId }} - Date: {{ record.date }}
              </div>
              <div
                *ngIf="storageRecords().length === 0"
                class="text-gray-500 text-sm">
                No records found
              </div>
            </div>
          </div>

          <!-- Dont Show Again Records -->
          <div class="bg-orange-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">
              🔒 Dont Show Again Records
            </h3>
            <div class="space-y-2 max-h-40 overflow-auto">
              <div
                *ngFor="let record of dontShowAgainRecords()"
                class="bg-white p-2 rounded border text-sm">
                <strong>{{ record.popupId }}</strong> - Branch:
                {{ record.branchId }} - Date:
                {{ record.timestamp | date: 'short' }}
              </div>
              <div
                *ngIf="dontShowAgainRecords().length === 0"
                class="text-gray-500 text-sm">
                No dont show again records found
              </div>
            </div>
          </div>
        </div>
      </aplz-ui-card>
    </div>
  `,
})
export class PopupDemoComponent {
  private popupTriggerService = inject(PopupTriggerService);
  private popupStorageService = inject(PopupStorageService);
  private popupRepository = inject(PopupRepository);
  private storeService = inject(StoreService);

  // Widget open/close state
  isOpen = signal(false);

  // Reactive state
  isLoading = signal(false);
  lastResult = signal<any>(null);
  currentBranchId = signal<string>('');
  today = signal<string>('');
  availablePopups = signal<any[]>([]);
  shownToday = signal<number>(0);
  dontShowAgain = signal<any[]>([]);
  eligiblePopups = signal<any[]>([]);
  storageRecords = signal<any[]>([]);
  dontShowAgainRecords = signal<any[]>([]);

  // Test configuration
  testPopupId = 'popup-1';

  // Configuration
  config = {
    branchId: '36',
    triggerType: 'manual',
    customHtml: `
<div style="width: 900px; max-width: 98vw; min-width: 500px; background: #7eeaff; border-radius: 32px; box-shadow: 0 4px 24px rgba(0,0,0,0.18); padding: 32px 24px; min-height: 340px; font-family: 'Montserrat', Arial, sans-serif; display: flex;">
  <!-- Columna izquierda -->
  <div style="flex: 2; padding-right: 24px;">
    <div style="font-size: 2.1rem; font-weight: bold; margin-bottom: 12px; color: #111;">
      ¿Tienes problemas para generar el ticket? <br>¡Resuelve en segundos!
    </div>
    <div style="font-size: 1.15rem; margin-bottom: 10px;">
      📋 Sigue los pasos para reintentar el proceso de cobro en <b>POSUI</b>.
    </div>
    <div style="font-size: 1.15rem; margin-bottom: 10px;">
      🎥 Consulta el <b>video tutorial disponible</b> en la sección Aplazoversity.
    </div>
    <div style="font-size: 1.15rem; margin-bottom: 24px;">
      ✅ ¡Así evitas perder la venta!
    </div>
    <button style="background: #111; color: #fff; border: none; border-radius: 24px; padding: 12px 36px; font-size: 1.2rem; font-weight: bold; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.10);">
      Ver tutorial
    </button>
  </div>
  <!-- Columna derecha -->
  <div style="flex: 1; background: #fff; border-radius: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.10); padding: 18px 12px; display: flex; flex-direction: column; align-items: center; justify-content: center; min-width: 180px; max-width: 220px;">
    <div style="font-size: 1.3rem; font-weight: bold; color: #111; margin-bottom: 8px; text-align: center;">
      ¿Sin señal?
    </div>
    <div style="font-size: 0.95rem; color: #222; text-align: center; margin-bottom: 8px;">
      Con NIP cobra con APLAZO sin internet
    </div>
    <!-- Simulación de imagen WiFi -->
    <div style="margin: 12px 0;">
      <svg width="90" height="60" viewBox="0 0 90 60">
        <ellipse cx="45" cy="50" rx="40" ry="8" fill="#7eeaff" opacity="0.5"/>
        <ellipse cx="45" cy="40" rx="30" ry="6" fill="#7eeaff" opacity="0.7"/>
        <ellipse cx="45" cy="30" rx="20" ry="4" fill="#7eeaff" opacity="0.9"/>
        <ellipse cx="45" cy="22" rx="10" ry="2" fill="#7eeaff"/>
      </svg>
    </div>
    <div style="display: flex; justify-content: space-between; width: 100%; font-size: 0.9em; margin-bottom: 6px;">
      <span>Rápido</span>
      <span>Fácil</span>
    </div>
    <button style="background: #111; color: #fff; border: none; border-radius: 16px; padding: 6px 18px; font-size: 1rem; font-weight: 500; cursor: pointer; margin-top: 6px;">
      Conoce NIP
    </button>
  </div>
</div>  
    `,
    popupWidth: 980,
    popupHeight: undefined as number | undefined,
  };

  constructor() {
    this.initializeState();
  }

  private initializeState() {
    this.today.set(new Date().toISOString().split('T')[0]);

    // Get current branch
    this.storeService.selectedBranch$.subscribe(branch => {
      if (branch?.id) {
        this.currentBranchId.set(String(branch.id));
        this.config.branchId = String(branch.id);
        this.loadAvailablePopups();
        this.loadDebugInfo();
      }
    });

    this.loadStorageRecords();
    this.loadDontShowAgainRecords();
  }

  private loadAvailablePopups() {
    if (this.config.branchId) {
      const obs = this.popupRepository.getAvailablePopups(this.config.branchId);
      if (obs && typeof obs.subscribe === 'function') {
        obs.subscribe(popups => {
          this.availablePopups.set(popups);
          this.updateShownToday();
        });
      } else {
        // fallback para tests rotos
        this.availablePopups.set([]);
        this.updateShownToday();
      }
    }
  }

  private loadDebugInfo() {
    if (this.config.branchId) {
      this.popupTriggerService
        .getDebugInfo(this.config.branchId)
        .subscribe(debugInfo => {
          this.shownToday.set(debugInfo.shownToday.length);
          this.dontShowAgain.set(debugInfo.dontShowAgain);
          this.eligiblePopups.set(debugInfo.eligiblePopups);
        });
    }
  }

  private updateShownToday() {
    const today = this.today();
    const shownCount = this.availablePopups().filter(popup =>
      this.popupStorageService.wasPopupShownToday(
        popup.popupId,
        this.config.branchId,
        today
      )
    ).length;
    this.shownToday.set(shownCount);
  }

  private loadStorageRecords() {
    // Access public method for demo purposes
    const records = this.popupStorageService.getShownRecords();
    this.storageRecords.set(records || []);
  }

  private loadDontShowAgainRecords() {
    // Access public method for demo purposes
    const records = this.popupStorageService.getDontShowAgainRecords();
    this.dontShowAgainRecords.set(records || []);
  }

  // Checkbox testing methods
  isPopupDontShowAgain(popupId?: string): boolean {
    const id = popupId || this.testPopupId;
    return this.popupStorageService.isDontShowAgain(id, this.config.branchId);
  }

  isPopupShownToday(popupId: string): boolean {
    return this.popupStorageService.wasPopupShownToday(
      popupId,
      this.config.branchId,
      this.today()
    );
  }

  markAsDontShowAgain() {
    this.popupStorageService.saveDontShowAgain(
      this.testPopupId,
      this.config.branchId
    );
    this.loadDontShowAgainRecords();
    this.loadDebugInfo();

    this.lastResult.set({
      action: 'mark_dont_show_again',
      popupId: this.testPopupId,
      branchId: this.config.branchId,
      timestamp: new Date().toISOString(),
    });
  }

  clearDontShowAgain() {
    // Note: This would need a method in PopupStorageService to clear specific records
    // For now, we'll clear all records for the branch
    this.popupStorageService.clearDontShowAgainRecordsForBranch(
      this.config.branchId
    );
    this.loadDontShowAgainRecords();
    this.loadDebugInfo();

    this.lastResult.set({
      action: 'clear_dont_show_again',
      branchId: this.config.branchId,
      timestamp: new Date().toISOString(),
    });
  }

  async triggerPopup() {
    this.isLoading.set(true);
    try {
      let result: boolean;
      if (this.config.triggerType === 'manual' && this.config.customHtml) {
        // Mostrar popup con HTML personalizado
        const event = new CustomEvent('popup-demo-html', {
          detail: {
            html: this.config.customHtml,
            width: this.config.popupWidth,
            height: this.config.popupHeight,
          },
        });
        window.dispatchEvent(event);
        result = true;
      } else {
        switch (this.config.triggerType) {
          case 'login':
            result =
              (await this.popupTriggerService
                .triggerAfterLogin(this.config.branchId)
                .toPromise()) ?? false;
            break;
          case 'payment':
            result =
              (await this.popupTriggerService
                .triggerAfterPaymentSuccess(this.config.branchId)
                .toPromise()) ?? false;
            break;
          default:
            result =
              (await this.popupTriggerService
                .triggerPopupIfNeeded({
                  branchId: this.config.branchId,
                })
                .toPromise()) ?? false;
        }
      }
      this.lastResult.set({
        success: result,
        timestamp: new Date().toISOString(),
        config: { ...this.config },
        triggerType: this.config.triggerType,
      });
      // Refresh state
      this.loadAvailablePopups();
      this.loadDebugInfo();
      this.loadStorageRecords();
      this.loadDontShowAgainRecords();
    } catch (error) {
      this.lastResult.set({
        error: error,
        timestamp: new Date().toISOString(),
        config: { ...this.config },
      });
    } finally {
      this.isLoading.set(false);
    }
  }

  checkPopupStatus() {
    this.popupTriggerService
      .shouldShowPopup({
        branchId: this.config.branchId,
      })
      .subscribe(shouldShow => {
        this.lastResult.set({
          shouldShow,
          timestamp: new Date().toISOString(),
          config: { ...this.config },
          action: 'status_check',
        });
      });
  }

  clearTodayRecords() {
    this.popupStorageService.clearRecordsForDate(this.today());
    this.loadStorageRecords();
    this.updateShownToday();
    this.loadDebugInfo();

    this.lastResult.set({
      action: 'clear_today',
      timestamp: new Date().toISOString(),
      date: this.today(),
    });
  }

  clearAllRecords() {
    this.popupStorageService.clearAllRecords();
    this.loadStorageRecords();
    this.loadDontShowAgainRecords();
    this.updateShownToday();
    this.loadDebugInfo();

    this.lastResult.set({
      action: 'clear_all',
      timestamp: new Date().toISOString(),
    });
  }
}
