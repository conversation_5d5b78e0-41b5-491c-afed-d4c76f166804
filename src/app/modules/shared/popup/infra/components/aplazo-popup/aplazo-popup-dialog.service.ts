import { Injectable, inject } from '@angular/core';
import { DialogService } from '@ngneat/dialog';
import { AplazoPopupComponent } from './aplazo-popup.component';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AplazoPopupDialogService {
  private dialog = inject(DialogService);

  /**
   * Abre el diálogo con el HTML del popup.
   * @param htmlContent - Contenido HTML del popup
   * @returns Observable que emite cuando el diálogo se cierra
   */
  openPopup(htmlContent: string): Observable<any> {
    console.log(
      '🎭 [AplazoPopupDialogService] Opening popup with HTML content, length:',
      htmlContent.length
    );
    console.log('🎭 [AplazoPopupDialogService] HTML content:', htmlContent);

    console.log(
      '🎭 [AplazoPopupDialogService] Checking DialogService availability:',
      !!this.dialog
    );

    if (!this.dialog) {
      console.error(
        '🎭 [AplazoPopupDialogService] DialogService is not initialized. Cannot open dialog.'
      );
      return of(null); // Return fallback if DialogService is not available
    }

    let dialogRef;
    try {
      dialogRef = this.dialog.open(AplazoPopupComponent, {
        data: { htmlContent },
        maxWidth: '600px',
        enableClose: true,
      });
      console.log('🎭 [AplazoPopupDialogService] Dialog opened successfully');
    } catch (error) {
      console.error(
        '🎭 [AplazoPopupDialogService] Error opening dialog:',
        error
      );
      return of(null); // Return fallback if opening dialog fails
    }

    // Check if dialogRef and afterClosed$ are defined to prevent errors
    if (dialogRef && dialogRef.afterClosed$) {
      console.log(
        '🎭 [AplazoPopupDialogService] afterClosed$ is available, using it for dialog closure'
      );
      return dialogRef.afterClosed$;
    } else {
      console.error(
        '🎭 [AplazoPopupDialogService] DialogRef or afterClosed$ is undefined. Dialog may not function as expected.'
      );
      console.error(
        '🎭 [AplazoPopupDialogService] Diagnostic - dialogRef exists:',
        !!dialogRef
      );
      console.error(
        '🎭 [AplazoPopupDialogService] Diagnostic - afterClosed$ property exists:',
        dialogRef ? !!dialogRef.afterClosed$ : 'dialogRef is undefined'
      );
      return of(null); // Return a fallback Observable to prevent errors
    }
  }

  /**
   * Método legacy para compatibilidad - ahora usa openPopup directamente
   * @deprecated Use openPopup(htmlContent) instead
   */
  openPopupForBranch() {
    console.warn(
      'openPopupForBranch is deprecated. Use openPopup(htmlContent) instead.'
    );
    // Por ahora, mostrar un popup de prueba
    this.openPopup(
      '<h2>Popup de prueba</h2><p>Este es un popup de prueba.</p>'
    );
  }
}
