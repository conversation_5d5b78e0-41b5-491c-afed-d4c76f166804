import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, catchError, map, tap } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../../../app-core/domain/environments';
import { PopupEntity } from '../../domain/entities/popup.entity';
import { PopupId } from '../../domain/value-objects/popup-id.value-object';

// API Response interface matching backend structure (LEGACY - for backward compatibility)
export interface PopupApiResponse {
  popupId: string;
  title: string;
  content: string;
  priority: number;
  isActive: boolean;
  createdAt: string;
  startAt: string;
  endAt: string;
}

// NEW: Real API Response interface for metadata endpoint
export interface PopupMetaResponse {
  popupId: string;
  startAt: string;
  endAt: string;
  priority: number;
}

export interface PopupMeta {
  popupId: string;
  startAt: string;
  endAt: string;
  priority: number;
}

@Injectable({ providedIn: 'root' })
export class PopupRepository {
  private http = inject(HttpClient);
  private environment = inject<PosEnvironmentCoreType>(POS_ENVIRONMENT_CORE);
  private apiUrl = this.environment.promoApiUrl;

  /**
   * Maps API response to PopupEntity instances (LEGACY)
   * @param apiResponses - Array of API response objects
   * @returns Array of PopupEntity instances
   */
  private mapApiResponseToEntities(
    apiResponses: PopupApiResponse[]
  ): PopupEntity[] {
    return apiResponses
      .map(response => this.mapSingleApiResponseToEntity(response))
      .filter(entity => entity !== null) as PopupEntity[];
  }

  /**
   * Maps metadata responses from real API to PopupEntity instances
   * @param metaResponses - Array of metadata response objects
   * @returns Array of PopupEntity instances
   */
  private mapMetaResponseToEntities(
    metaResponses: PopupMetaResponse[]
  ): PopupEntity[] {
    return metaResponses
      .map(response => this.mapMetaToEntity(response))
      .filter(entity => entity !== null) as PopupEntity[];
  }

  /**
   * Maps a single API response to PopupEntity (LEGACY)
   * @param response - Single API response object
   * @returns PopupEntity instance or null if mapping fails
   */
  private mapSingleApiResponseToEntity(
    response: PopupApiResponse
  ): PopupEntity | null {
    try {
      const popupId = PopupId.fromString(response.popupId);
      const startAt = response.startAt ? new Date(response.startAt) : undefined;
      const endAt = response.endAt ? new Date(response.endAt) : undefined;
      const createdAt = new Date(response.createdAt);

      return new PopupEntity(
        popupId,
        response.title,
        response.content,
        response.priority,
        response.isActive,
        createdAt,
        startAt,
        endAt
      );
    } catch (error) {
      console.error(
        'Error mapping API response to PopupEntity:',
        error,
        response
      );
      return null;
    }
  }

  /**
   * Maps metadata response from real API to PopupEntity
   * @param metaResponse - Metadata from real API
   * @returns PopupEntity instance or null if mapping fails
   */
  private mapMetaToEntity(metaResponse: PopupMetaResponse): PopupEntity | null {
    try {
      const popupId = PopupId.fromString(metaResponse.popupId);
      const startAt = metaResponse.startAt
        ? new Date(metaResponse.startAt)
        : undefined;
      const endAt = metaResponse.endAt
        ? new Date(metaResponse.endAt)
        : undefined;

      // Default values for fields not provided by real API
      const defaultTitle = `Popup ${metaResponse.popupId}`;
      const defaultContent = ''; // Will be loaded lazily
      const defaultIsActive = true; // Assume active if returned by API
      const defaultCreatedAt = new Date(); // Use current date as fallback

      return new PopupEntity(
        popupId,
        defaultTitle,
        defaultContent,
        metaResponse.priority,
        defaultIsActive,
        defaultCreatedAt,
        startAt,
        endAt
      );
    } catch (error) {
      console.error(
        'Error mapping metadata response to PopupEntity:',
        error,
        metaResponse
      );
      return null;
    }
  }

  /**
   * GET /api/v1/popups?branchId={branchId}
   * Obtiene los popups disponibles para una branch específica
   * @param branchId - The branch ID to fetch popups for
   * @returns Observable that emits an array of PopupEntity instances
   */
  getAvailablePopups(branchId: string): Observable<PopupEntity[]> {
    console.log(
      '🔍 [PopupRepository] Getting available popups for branch:',
      branchId
    );

    // Use real API endpoint with fallback to mocks
    return this.http
      .get<PopupMetaResponse[]>(
        `${this.apiUrl}/api/v1/popups?branchId=${branchId}`
      )
      .pipe(
        map(metaResponses => this.mapMetaResponseToEntities(metaResponses)),
        catchError((error: unknown) => {
          console.warn(
            '⚠️ [PopupRepository] Error fetching popups from real API, using mock data:',
            error
          );
          const mockPopups = this.getMockPopups();
          return of(this.mapApiResponseToEntities(mockPopups));
        })
      );
  }

  /**
   * GET /api/v1/popups/content/{popupId}
   * Obtiene el HTML de un popup específico
   */
  getPopupHtml(popupId: string): Observable<string> {
    console.log('🔍 [PopupRepository] Getting HTML for popup:', popupId);

    // Use real API endpoint with fallback to mocks
    return this.http
      .get(`${this.apiUrl}/api/v1/popups/content/${popupId}`, {
        responseType: 'text', // Important: expect HTML string, not JSON
      })
      .pipe(
        tap(() => {
          console.log(
            '✅ [PopupRepository] Successfully fetched HTML for popup:',
            popupId
          );
        }),
        catchError((error: unknown) => {
          console.warn(
            '⚠️ [PopupRepository] Error fetching popup HTML from real API, using mock data:',
            error
          );
          return this.getMockPopupHtml(popupId);
        })
      );
  }

  /**
   * Mock data como fallback cuando el API no está disponible
   */
  private getMockPopups(): PopupApiResponse[] {
    return [
      {
        popupId: 'POPUP-001',
        title: 'Problemas con el ticket',
        content: '¿Tienes problemas para generar el ticket?',
        priority: 1,
        isActive: true,
        createdAt: '2025-06-23T00:00:00.000Z',
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
      },
      {
        popupId: 'POPUP-002',
        title: 'Sin señal',
        content: '¿Sin señal?',
        priority: 2,
        isActive: true,
        createdAt: '2025-06-23T00:00:00.000Z',
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
      },
    ];
  }

  /**
   * Mock HTML como fallback cuando el API no está disponible
   */
  private getMockPopupHtml(popupId: string): Observable<string> {
    if (popupId === 'POPUP-001') {
      return of('<h2>¿Tienes problemas para generar el ticket?</h2>');
    }
    if (popupId === 'POPUP-002') {
      return of('<h2>¿Sin señal?</h2>');
    }
    return of('<h2>Popup no encontrado</h2>');
  }
}
