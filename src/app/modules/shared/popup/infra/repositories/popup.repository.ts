import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, catchError, map } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../../../app-core/domain/environments';
import { PopupEntity } from '../../domain/entities/popup.entity';
import { PopupId } from '../../domain/value-objects/popup-id.value-object';

// API Response interface matching backend structure
export interface PopupApiResponse {
  popupId: string;
  title: string;
  content: string;
  priority: number;
  isActive: boolean;
  createdAt: string;
  startAt: string;
  endAt: string;
}

export interface PopupMeta {
  popupId: string;
  startAt: string;
  endAt: string;
  priority: number;
}

@Injectable({ providedIn: 'root' })
export class PopupRepository {
  private http = inject(HttpClient);
  private environment = inject<PosEnvironmentCoreType>(POS_ENVIRONMENT_CORE);
  private apiUrl = this.environment.promoApiUrl;

  /**
   * Maps API response to PopupEntity instances
   * @param apiResponses - Array of API response objects
   * @returns Array of PopupEntity instances
   */
  private mapApiResponseToEntities(
    apiResponses: PopupApiResponse[]
  ): PopupEntity[] {
    return apiResponses
      .map(response => this.mapSingleApiResponseToEntity(response))
      .filter(entity => entity !== null) as PopupEntity[];
  }

  /**
   * Maps a single API response to PopupEntity
   * @param response - Single API response object
   * @returns PopupEntity instance or null if mapping fails
   */
  private mapSingleApiResponseToEntity(
    response: PopupApiResponse
  ): PopupEntity | null {
    try {
      const popupId = PopupId.fromString(response.popupId);
      const startAt = response.startAt ? new Date(response.startAt) : undefined;
      const endAt = response.endAt ? new Date(response.endAt) : undefined;
      const createdAt = new Date(response.createdAt);

      return new PopupEntity(
        popupId,
        response.title,
        response.content,
        response.priority,
        response.isActive,
        createdAt,
        startAt,
        endAt
      );
    } catch (error) {
      console.error(
        'Error mapping API response to PopupEntity:',
        error,
        response
      );
      return null;
    }
  }

  /**
   * GET /api/v1/popups?branchId={branchId}
   * Obtiene los popups disponibles para una branch específica
   * @param branchId - The branch ID to fetch popups for
   * @returns Observable that emits an array of PopupEntity instances
   */
  getAvailablePopups(branchId: string): Observable<PopupEntity[]> {
    console.log(
      '🔍 [PopupRepository] Getting available popups for branch:',
      branchId
    );

    // Use mock data for now, will be replaced with real API call
    const mockPopups = this.getMockPopups();
    console.log('🔍 [PopupRepository] Returning mock popups:', mockPopups);
    return of(mockPopups).pipe(
      map(apiResponses => this.mapApiResponseToEntities(apiResponses))
    );

    // TODO: Uncomment when backend is ready
    /*
    return this.http
      .get<PopupApiResponse[]>(`${this.apiUrl}/api/v1/popups?branchId=${branchId}`)
      .pipe(
        map(apiResponses => this.mapApiResponseToEntities(apiResponses)),
        catchError((error: any) => {
          console.warn(
            'Error fetching popups from API, using mock data:',
            error
          );
          const mockPopups = this.getMockPopups();
          return of(this.mapApiResponseToEntities(mockPopups));
        })
      );
    */
  }

  /**
   * GET /api/v1/popups/{popupId}
   * Obtiene el HTML de un popup específico
   */
  getPopupHtml(popupId: string): Observable<string> {
    // Por ahora, usar mocks por defecto para evitar errores 401
    // TODO: Implementar verificación de autenticación cuando el backend esté listo
    console.log('🔍 [PopupRepository] Getting HTML for popup:', popupId);
    const mockHtml = this.getMockPopupHtml(popupId);
    console.log('🔍 [PopupRepository] Returning mock HTML for popup:', popupId);
    return mockHtml;

    // Código original comentado para cuando el backend esté listo:
    /*
    return this.http
      .get<string>(`${this.apiUrl}/api/v1/popups/${popupId}/html`)
      .pipe(
        catchError((error: any) => {
          console.warn(
            'Error fetching popup HTML from API, using mock data:',
            error
          );
          return this.getMockPopupHtml(popupId);
        })
      );
    */
  }

  /**
   * Mock data como fallback cuando el API no está disponible
   */
  private getMockPopups(): PopupApiResponse[] {
    return [
      {
        popupId: 'POPUP-001',
        title: 'Problemas con el ticket',
        content: '¿Tienes problemas para generar el ticket?',
        priority: 1,
        isActive: true,
        createdAt: '2025-06-23T00:00:00.000Z',
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
      },
      {
        popupId: 'POPUP-002',
        title: 'Sin señal',
        content: '¿Sin señal?',
        priority: 2,
        isActive: true,
        createdAt: '2025-06-23T00:00:00.000Z',
        startAt: '2025-06-23T00:00:00.000Z',
        endAt: '2025-07-23T00:00:00.000Z',
      },
    ];
  }

  /**
   * Mock HTML como fallback cuando el API no está disponible
   */
  private getMockPopupHtml(popupId: string): Observable<string> {
    if (popupId === 'POPUP-001') {
      return of('<h2>¿Tienes problemas para generar el ticket?</h2>');
    }
    if (popupId === 'POPUP-002') {
      return of('<h2>¿Sin señal?</h2>');
    }
    return of('<h2>Popup no encontrado</h2>');
  }
}
