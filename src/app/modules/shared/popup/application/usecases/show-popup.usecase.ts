import { Injectable, inject } from '@angular/core';
import { Observable, of } from 'rxjs';
import { switchMap, take, map } from 'rxjs/operators';
import {
  PopupRepository,
  PopupMeta,
} from '../../infra/repositories/popup.repository';
import { PopupEntity } from '../../domain/entities/popup.entity';
import { AplazoPopupDialogService } from '../../infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { PopupStorageService } from '../services/popup-storage.service';
import { StoreService } from 'src/app/core/application/services/store.service';

export interface ShowPopupResult {
  success: boolean;
  popupShown?: PopupEntity;
  reason?: string;
}

/**
 * Use case para mostrar popups con lógica de negocio completa.
 * Maneja la selección de popups, validaciones y estado de mostrado.
 */
@Injectable({ providedIn: 'root' })
export class ShowPopupUseCase {
  private popupRepo = inject(PopupRepository);
  private dialogService = inject(AplazoPopupDialogService);
  private storageService = inject(PopupStorageService);
  private storeService = inject(StoreService);

  /**
   * Ejecuta la lógica de negocio para mostrar un popup.
   * @returns Observable con el resultado de la operación
   */
  execute(): Observable<ShowPopupResult> {
    console.log('🚀 [ShowPopupUseCase] Starting popup execution');

    return this.storeService.selectedBranch$.pipe(
      take(1),
      switchMap(branch => {
        const branchId = branch?.id ? String(branch.id) : undefined;
        console.log('🚀 [ShowPopupUseCase] Branch ID:', branchId);

        if (!branchId) {
          console.log('❌ [ShowPopupUseCase] No branch selected');
          return of({ success: false, reason: 'No branch selected' });
        }

        return this.popupRepo.getAvailablePopups(branchId).pipe(
          switchMap(popups => {
            console.log('🚀 [ShowPopupUseCase] Available popups:', popups);

            if (!popups || popups.length === 0) {
              console.log('❌ [ShowPopupUseCase] No popups available');
              return of({ success: false, reason: 'No popups available' });
            }

            // Filtrar popups ya mostrados hoy - usar el mismo formato que PopupTriggerService
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            console.log('🚀 [ShowPopupUseCase] Today (YYYY-MM-DD):', today);

            const unshownPopups = popups.filter(
              popup =>
                !this.storageService.wasPopupShownToday(
                  popup.popupId,
                  branchId,
                  today
                ) &&
                !this.storageService.isDontShowAgain(popup.popupId, branchId)
            );

            console.log(
              '🚀 [ShowPopupUseCase] Unshown popups (after filters):',
              unshownPopups
            );

            if (unshownPopups.length === 0) {
              console.log(
                '❌ [ShowPopupUseCase] All popups already shown today or marked as dont show again'
              );
              return of({
                success: false,
                reason:
                  'All popups already shown today or marked as dont show again',
              });
            }

            // Seleccionar el popup de mayor prioridad
            const selectedPopup = unshownPopups.reduce((prev, curr) =>
              curr.priority > prev.priority ? curr : prev
            );

            console.log('🚀 [ShowPopupUseCase] Selected popup:', selectedPopup);

            // Obtener el HTML del popup seleccionado
            return this.popupRepo.getPopupHtml(selectedPopup.popupId).pipe(
              map(htmlContent => {
                console.log(
                  '🚀 [ShowPopupUseCase] Got HTML content, length:',
                  htmlContent.length
                );

                // Marcar como mostrado
                this.storageService.markPopupAsShown(
                  selectedPopup.popupId,
                  branchId,
                  today
                );

                console.log('🚀 [ShowPopupUseCase] Opening popup dialog...');

                // Mostrar el popup
                this.dialogService.openPopup(htmlContent);

                console.log(
                  '✅ [ShowPopupUseCase] Popup execution completed successfully'
                );

                return {
                  success: true,
                  popupShown: selectedPopup,
                };
              })
            );
          })
        );
      })
    );
  }

  /**
   * Verifica si hay popups disponibles para mostrar.
   * @returns Observable con true si hay popups disponibles
   */
  hasAvailablePopups(): Observable<boolean> {
    return this.storeService.selectedBranch$.pipe(
      take(1),
      switchMap(branch => {
        const branchId = branch?.id ? String(branch.id) : undefined;

        if (!branchId) {
          return of(false);
        }

        return this.popupRepo.getAvailablePopups(branchId).pipe(
          switchMap(popups => {
            if (!popups || popups.length === 0) {
              return of(false);
            }

            // Usar el mismo formato de fecha que PopupTriggerService
            const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            const unshownPopups = popups.filter(
              popup =>
                !this.storageService.wasPopupShownToday(
                  popup.popupId,
                  branchId,
                  today
                ) &&
                !this.storageService.isDontShowAgain(popup.popupId, branchId)
            );

            return of(unshownPopups.length > 0);
          })
        );
      })
    );
  }
}
