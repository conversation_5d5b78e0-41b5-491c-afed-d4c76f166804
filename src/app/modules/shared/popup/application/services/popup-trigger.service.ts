import { Injectable, inject } from '@angular/core';
import { Observable, of, switchMap, map } from 'rxjs';
import { PopupRepository } from '../../infra/repositories/popup.repository';
import { PopupStorageService } from './popup-storage.service';
import {
  ShowPopupUseCase,
  ShowPopupResult,
} from '../usecases/show-popup.usecase';

export interface PopupTriggerConfig {
  branchId: string;
  checkPaymentSuccess?: boolean;
  checkLoginSuccess?: boolean;
}

/**
 * Servicio que orquesta cuándo y cómo mostrar popups.
 * Coordina la lógica de negocio para determinar si un popup debe mostrarse.
 */
@Injectable({ providedIn: 'root' })
export class PopupTriggerService {
  private popupRepository = inject(PopupRepository);
  private popupStorage = inject(PopupStorageService);
  private showPopupUseCase = inject(ShowPopupUseCase);

  /**
   * Verifica si hay popups disponibles y si deben mostrarse.
   * @param config - Configuración del trigger
   * @returns Observable que emite true si debe mostrarse un popup
   */
  shouldShowPopup(config: PopupTriggerConfig): Observable<boolean> {
    console.log(
      '🎯 [PopupTriggerService] shouldShowPopup called with config:',
      config
    );

    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

    return this.popupRepository.getAvailablePopups(config.branchId).pipe(
      switchMap(popups => {
        console.log(
          '🎯 [PopupTriggerService] shouldShowPopup: Available popups:',
          popups
        );

        if (popups.length === 0) {
          console.log(
            '🎯 [PopupTriggerService] shouldShowPopup: No popups available, returning false'
          );
          return of(false);
        }

        // Ordenar por prioridad (mayor número = mayor prioridad)
        const sortedPopups = popups.sort((a, b) => b.priority - a.priority);
        console.log(
          '🎯 [PopupTriggerService] shouldShowPopup: Sorted popups by priority:',
          sortedPopups
        );

        // Buscar el primer popup que no haya sido mostrado hoy Y no esté marcado como "No mostrar de nuevo"
        for (const popup of sortedPopups) {
          const wasShown = this.popupStorage.wasPopupShownToday(
            popup.popupId,
            config.branchId,
            today
          );
          const isDontShowAgain = this.popupStorage.isDontShowAgain(
            popup.popupId,
            config.branchId
          );

          console.log(
            `🎯 [PopupTriggerService] shouldShowPopup: Popup ${popup.popupId} - was shown today: ${wasShown}, is dont show again: ${isDontShowAgain}`
          );

          if (!wasShown && !isDontShowAgain) {
            console.log(
              `🎯 [PopupTriggerService] shouldShowPopup: Found eligible popup ${popup.popupId}, returning true`
            );
            return of(true);
          }
        }

        console.log(
          '🎯 [PopupTriggerService] shouldShowPopup: All popups already shown today or marked as dont show again, returning false'
        );
        return of(false);
      })
    );
  }

  /**
   * Ejecuta la lógica completa de mostrar popup si corresponde.
   * @param config - Configuración del trigger
   * @returns Observable que emite el resultado de la operación
   */
  triggerPopupIfNeeded(config: PopupTriggerConfig): Observable<boolean> {
    console.log(
      '🎯 [PopupTriggerService] triggerPopupIfNeeded called with config:',
      config
    );

    return this.shouldShowPopup(config).pipe(
      switchMap(shouldShow => {
        console.log(
          '🎯 [PopupTriggerService] triggerPopupIfNeeded: shouldShow =',
          shouldShow
        );

        if (!shouldShow) {
          console.log(
            '🎯 [PopupTriggerService] triggerPopupIfNeeded: No popup should be shown, returning false'
          );
          return of(false);
        }

        console.log(
          '🎯 [PopupTriggerService] triggerPopupIfNeeded: Should show popup, calling showPopupUseCase.execute()'
        );
        return this.showPopupUseCase.execute().pipe(
          map((result: ShowPopupResult) => {
            console.log(
              '🎯 [PopupTriggerService] triggerPopupIfNeeded: showPopupUseCase result:',
              result
            );
            return result.success;
          })
        );
      })
    );
  }

  /**
   * Trigger específico para después de login exitoso.
   * @param branchId - ID de la branch
   * @returns Observable que emite el resultado
   */
  triggerAfterLogin(branchId: string): Observable<boolean> {
    return this.triggerPopupIfNeeded({
      branchId,
      checkLoginSuccess: true,
    });
  }

  /**
   * Trigger específico para después de pago exitoso.
   * @param branchId - ID de la branch
   * @returns Observable que emite el resultado
   */
  triggerAfterPaymentSuccess(branchId: string): Observable<boolean> {
    return this.triggerPopupIfNeeded({
      branchId,
      checkPaymentSuccess: true,
    });
  }

  /**
   * Limpia registros antiguos de popups mostrados.
   */
  cleanupOldRecords(): void {
    this.popupStorage.cleanupOldRecords();
  }

  /**
   * Obtiene información de debug sobre el estado de popups para una branch.
   * @param branchId - ID de la branch
   * @returns Observable con información de debug
   */
  getDebugInfo(branchId: string): Observable<{
    availablePopups: any[];
    shownToday: any[];
    dontShowAgain: any[];
    eligiblePopups: any[];
  }> {
    const today = new Date().toISOString().split('T')[0];

    return this.popupRepository.getAvailablePopups(branchId).pipe(
      map(popups => {
        const shownToday = popups.filter(popup =>
          this.popupStorage.wasPopupShownToday(popup.popupId, branchId, today)
        );

        const dontShowAgain = popups.filter(popup =>
          this.popupStorage.isDontShowAgain(popup.popupId, branchId)
        );

        const eligiblePopups = popups.filter(
          popup =>
            !this.popupStorage.wasPopupShownToday(
              popup.popupId,
              branchId,
              today
            ) && !this.popupStorage.isDontShowAgain(popup.popupId, branchId)
        );

        return {
          availablePopups: popups,
          shownToday,
          dontShowAgain,
          eligiblePopups,
        };
      })
    );
  }
}
