@if (vm$ | async; as context) {
  <article class="px-4 lg:px-10 pt-5 pb-10">
    <h2 class="text-xl font-medium mb-3 uppercase">
      {{ context.textUI.title }}
    </h2>
  </article>

  <section class="px-4 lg:px-10 pb-[30vh]">
    <aplz-ui-card appearance="flat">
      <form #form="ngForm" (ngSubmit)="requestTraining(form)">
        <legend
          class="text-dark-primary text-light text-md text-pretty border-l border-special-info borde-4 p-4">
          Esta solicitud no garantiza la entrega del material. Será evaluada por
          nuestro equipo y nos pondremos en contacto para confirmar la
          disponibilidad y próximos pasos.
        </legend>
        <div class="w-full flex items-center mt-4">
          <aplz-ui-form-field>
            <aplz-ui-form-label> Tipo de entrenamiento * </aplz-ui-form-label>
            <select
              aplzFormSelect
              name="trainingType"
              [ngModel]="trainingType()"
              (ngModelChange)="trainingType.set($event)"
              required
              ngModel
              #trainingTypeInput="ngModel"
              data-test="training-type-select"
              class="p-4 min-w-[200px]">
              <option value="">Selecciona el tipo de entrenamiento</option>
              @for (type of trainingTypes; track type.value) {
                <option [value]="type.value">
                  {{ type.label }}
                </option>
              }
            </select>

            <ng-container aplzFormError>
              @if (trainingTypeInput.touched && trainingTypeInput.invalid) {
                @if (trainingTypeInput.getError('required')) {
                  <p>
                    {{ context.textUI.errors.required }}
                  </p>
                }
              }
            </ng-container>
          </aplz-ui-form-field>
        </div>

        <div class="grid gap-4 grid-cols-1 md:grid-cols-2">
          <aplz-ui-form-field>
            <aplz-ui-form-label>
              {{ context.textUI.form.contactName }}
            </aplz-ui-form-label>
            <input
              type="text"
              aplzFormInput
              name="contactName"
              [ngModel]="contactName()"
              (ngModelChange)="contactName.set($event)"
              required
              [minlength]="contactNameMinLength"
              ngModel
              #contactNameInput="ngModel"
              data-test="contact-name-input"
              aplazoTrimSpaces />

            <ng-container aplzFormError>
              @if (contactNameInput.touched && contactNameInput.invalid) {
                @if (contactNameInput.getError('required')) {
                  <p>
                    {{ context.textUI.errors.required }}
                  </p>
                }

                @if (contactNameInput.getError('minlength')) {
                  <p>
                    {{ context.textUI.errors.minlength }}
                  </p>
                }
              }
            </ng-container>
          </aplz-ui-form-field>

          <aplz-ui-form-field>
            <aplz-ui-form-label>
              {{ context.textUI.form.contactPhone }}
            </aplz-ui-form-label>
            <input
              type="text"
              aplzFormInput
              inputmode="tel"
              name="contactPhone"
              [ngModel]="contactPhone()"
              (ngModelChange)="contactPhone.set($event)"
              required
              [minlength]="phoneMinLength"
              ngModel
              #contactPhoneInput="ngModel"
              data-test="contact-phone-input"
              aplazoOnlyNumbers
              [aplazoTruncateLength]="phoneMinLength" />

            <ng-container aplzFormError>
              @if (contactPhoneInput.touched && contactPhoneInput.invalid) {
                @if (contactPhoneInput.getError('required')) {
                  <p>
                    {{ context.textUI.errors.required }}
                  </p>
                }

                @if (contactPhoneInput.getError('minlength')) {
                  <p>
                    {{ context.textUI.errors.phoneLength }}
                  </p>
                }
              }
            </ng-container>
          </aplz-ui-form-field>
        </div>

        <aplz-ui-form-field>
          <aplz-ui-form-label>
            {{ context.textUI.form.additionalComments }}
          </aplz-ui-form-label>
          <textarea
            aplzFormInput
            name="additionalComments"
            [ngModel]="additionalComments()"
            (ngModelChange)="additionalComments.set($event)"
            rows="5"
            ngModel
            data-test="additional-comments-input"
            #additionalCommentsInput="ngModel">
          </textarea>
        </aplz-ui-form-field>

        <div class="my-10">
          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            data-test="submit-button"
            [rounded]="true">
            {{ context.textUI.form.submit }}
          </button>
        </div>
      </form>
    </aplz-ui-card>
  </section>
}
