import { AsyncPipe } from '@angular/common';
import { Component, inject, signal } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { combineLatest, take } from 'rxjs';
import { RequestTrainingUsecase } from '../storefront-help/request-training.usecase';

@Component({
  selector: 'aplazo-training-form',
  standalone: true,
  imports: [
    FormsModule,
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AsyncPipe,
    OnlyNumbersDirective,
    AplazoTrimSpacesDirective,
    AplazoTruncateLengthDirective,
  ],
  templateUrl: './training-form.component.html',
})
export class TrainingFormComponent {
  readonly #notifier = inject(NotifierService);
  readonly #i18n = inject(I18NService);
  readonly #trainingUsecase = inject(RequestTrainingUsecase);

  readonly contactNameMinLength = 3;
  readonly phoneMinLength = 10;

  readonly trainingType = signal<string | null>(null);
  readonly contactName = signal<string | null>(null);
  readonly contactPhone = signal<string | null>(null);
  readonly additionalComments = signal<string | null>(null);

  readonly trainingTypes = [
    {
      value: 'conocimientos-generales',
      label: 'Conocimientos Generales Aplazo',
    },
    { value: 'practicos-cobro', label: 'Prácticos - Cobro' },
    { value: 'manejo-objeciones', label: 'Manejo de objeciones' },
  ];

  readonly textUI$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    form: {
      contactName: string;
      contactPhone: string;
      additionalComments: string;
      submit: string;
    };
    errors: {
      required: string;
      minlength: string;
      phoneLength: string;
    };
  }>({
    key: 'training',
    scope: 'storefront-help',
    params: {
      errors: {
        minlength: { minlength: this.contactNameMinLength },
        phoneLength: { phoneLength: this.phoneMinLength },
      },
    },
  });

  readonly vm$ = combineLatest({
    textUI: this.textUI$,
  });

  async requestTraining(form: NgForm): Promise<void> {
    form.form.markAllAsTouched();

    if (form.invalid) {
      this.#notifier.warning({
        title:
          'Asegúrate de que todos los campos estén completos o sean válidos',
      });

      return;
    }

    this.#trainingUsecase
      .execute({
        trainingType: this.trainingType() ?? '',
        contactName: this.contactName() ?? '',
        contactPhone: this.contactPhone() ?? '',
        additionalComments: this.additionalComments() ?? '',
      })
      .pipe(take(1))
      .subscribe(() => {
        this.#clearForm(form);
      });
  }

  #clearForm(form: NgForm): void {
    form.form.reset();
  }
}
