import { Route } from '@angular/router';
import { StoreSupportLayoutComponent } from '../../store-support-layout.component';
import { TrainingFormComponent } from '../../training-form.component';
import { PopMaterialComponent } from '../pages/pop-material/pop-material.component';

const storeSupportRoutes: Route[] = [
  {
    path: '',
    component: StoreSupportLayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'training',
        pathMatch: 'full',
      },
      {
        path: 'training',
        component: TrainingFormComponent,
      },
      {
        path: 'pop-materials',
        component: PopMaterialComponent,
      },
    ],
  },
];

export default storeSupportRoutes;
