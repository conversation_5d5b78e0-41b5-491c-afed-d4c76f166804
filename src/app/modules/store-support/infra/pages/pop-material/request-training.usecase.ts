import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  combineLatestWith,
  finalize,
  map,
  Observable,
  switchMap,
  take,
  tap,
  throwError,
} from 'rxjs';
import { StoreService } from 'src/app/core/application/services/store.service';
import { StorefrontHelpWithHttpRepository } from './storefront-help-with-http.repository';

export interface TrainingUIRequest {
  trainingType: string;
  contactName: string;
  contactPhone: string;
  additionalComments?: string;
}

@Injectable({
  providedIn: 'root',
})
export class RequestTrainingUsecase
  implements BaseUsecase<TrainingUIRequest, Observable<void>>
{
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #repository = inject(StorefrontHelpWithHttpRepository);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #store = inject(StoreService);

  execute(req: TrainingUIRequest): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      if (!Guard.againstNullOrUndefined(req, 'trainingType').succeeded) {
        throw new RuntimeMerchantError(
          'El tipo de entrenamiento es requerido',
          'RequestTrainingUsecase::empty::trainingType'
        );
      }

      if (!Guard.againstNullOrUndefined(req, 'contactName').succeeded) {
        throw new RuntimeMerchantError(
          'El nombre de contacto es requerido',
          'RequestTrainingUsecase::empty::contactName'
        );
      }

      if (!Guard.againstNullOrUndefined(req, 'contactPhone').succeeded) {
        throw new RuntimeMerchantError(
          'El teléfono de contacto es requerido',
          'RequestTrainingUsecase::empty::contactPhone'
        );
      }

      Guard.againstInvalidNumbers(
        +req.contactPhone,
        'RequestTrainingUsecase',
        'El teléfono no es válido'
      );

      return this.#store.getMerchantId$().pipe(
        combineLatestWith(this.#store.selectedBranch$.pipe(map(b => b?.id))),
        switchMap(([merchantId, storefrontId]) => {
          return this.#repository.requestTraining({
            trainingType: req.trainingType,
            contactName: req.contactName,
            contactPhone: req.contactPhone,
            merchantId: merchantId ? String(merchantId) : '',
            storefrontId: storefrontId ? String(storefrontId) : '',
            additionalComments: req.additionalComments,
          });
        }),
        catchError(e => this.#errorHandler.handle<never>(e)),
        tap(() => {
          this.#notifier.success({
            title: 'Hemos recibido tu solicitud.',
            message:
              'Pronto nos pondremos en contacto contigo para coordinar el entrenamiento.',
          });
        }),
        finalize(() => {
          this.#loader.hide(idLoader);
        }),
        take(1)
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
