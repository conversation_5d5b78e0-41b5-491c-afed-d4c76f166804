import { AsyncPipe } from '@angular/common';
import { Component, computed, inject, signal } from '@angular/core';
import { FormsModule, NgForm } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  AplazoTrimSpacesDirective,
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { combineLatest, take } from 'rxjs';
import { RequestPopMaterialUsecase } from '../../../application/usecases/request-pop-material.usecase';

@Component({
  standalone: true,
  selector: 'app-pop-material',
  templateUrl: './pop-material.component.html',
  imports: [
    FormsModule,
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AsyncPipe,
    OnlyNumbersDirective,
    AplazoTrimSpacesDirective,
    AplazoTruncateLengthDirective,
  ],
})
export class PopMaterialComponent {
  readonly #notifier = inject(NotifierService);
  readonly #i18n = inject(I18NService);
  readonly #popMaterialUsecase = inject(RequestPopMaterialUsecase);

  readonly nameMinLength = 3;
  readonly phoneMinLength = 10;

  readonly name = signal<string | null>(null);
  readonly phone = signal<string | null>(null);
  readonly comments = signal<string | null>(null);
  readonly hasTentCard = signal<boolean>(false);

  readonly materials = computed(() => {
    const materials: string[] = [];

    if (this.hasTentCard()) {
      materials.push('Tent Card');
    } else {
      const index = materials.indexOf('Tent Card');

      if (index !== -1) {
        materials.splice(index, 1);
      }
    }

    return materials;
  });

  readonly textUI$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    form: {
      tentCard: string;
      contact: string;
      phone: string;
      comments: string;
      submit: string;
    };
    errors: {
      required: string;
      minlength: string;
      phoneLength: string;
    };
  }>({
    key: 'popMaterial',
    scope: 'storefront-help',
    params: {
      errors: {
        minlength: { minlength: this.nameMinLength },
        phoneLength: { phoneLength: this.phoneMinLength },
      },
    },
  });

  readonly vm$ = combineLatest({
    textUI: this.textUI$,
  });

  async requestPopMaterial(form: NgForm): Promise<void> {
    form.form.markAllAsTouched();

    if (this.materials().length === 0) {
      this.#notifier.warning({
        title: 'Por favor, seleccione al menos un material',
      });

      return;
    }

    if (form.invalid) {
      this.#notifier.warning({
        title:
          'Asegúrate de que todos los campos estén completos o sean válidos',
      });

      return;
    }

    this.#popMaterialUsecase
      .execute({
        name: this.name() ?? '',
        phone: this.phone() ?? '',
        comments: this.comments() ?? '',
        hasTentCard: this.hasTentCard(),
      })
      .pipe(take(1))
      .subscribe(() => {
        this.#clearForm(form);
      });
  }

  #clearForm(form: NgForm): void {
    form.form.reset();
  }
}
