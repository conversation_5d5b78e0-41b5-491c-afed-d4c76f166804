@if (vm$ | async; as context) {
  <article class="px-4 lg:px-10 pt-5 pb-10">
    <h2 class="text-xl font-medium mb-3 uppercase">
      {{ context.textUI.title }}
    </h2>
  </article>

  <section class="px-4 lg:px-10 pb-[30vh]">
    <aplz-ui-card appearance="flat">
      <form #form="ngForm" (ngSubmit)="requestPopMaterial(form)">
        <legend
          class="text-dark-primary text-light text-md text-pretty border-l border-special-info borde-4 p-4">
          Esta solicitud no garantiza la entrega del material. Será evaluada por
          nuestro equipo y nos pondremos en contacto para confirmar la
          disponibilidad y próximos pasos.
        </legend>
        <fieldset
          class="border-dark-secondary border rounded-lg p-4 my-8"
          [class.border-special-danger]="
            tentCardInput.touched && materials().length === 0
          ">
          <legend
            class="mb-4 p-2"
            [class.text-special-danger]="
              tentCardInput.touched && materials().length === 0
            ">
            Material solicitado *
          </legend>

          <div class="flex items-center gap-x-2">
            <input
              id="tent-card-checkbox"
              type="checkbox"
              [ngModel]="hasTentCard()"
              (ngModelChange)="hasTentCard.set($event)"
              name="hasTentCard"
              ngModel
              #tentCardInput="ngModel"
              data-test="tent-card-checkbox"
              class="w-4 h-4 text-aplazo-aplazo bg-light border-dark-background rounded focus:ring-aplazo-aplazo focus:ring-2" />
            <label for="tent-card-checkbox">
              {{ context.textUI.form.tentCard }}
            </label>
          </div>
        </fieldset>

        <div class="grid gap-4 grid-cols-1 md:grid-cols-2">
          <aplz-ui-form-field>
            <aplz-ui-form-label>
              {{ context.textUI.form.contact }}
            </aplz-ui-form-label>
            <input
              type="text"
              aplzFormInput
              name="name"
              [ngModel]="name()"
              (ngModelChange)="name.set($event)"
              required
              [minlength]="nameMinLength"
              ngModel
              #nameInput="ngModel"
              data-test="name-input"
              aplazoTrimSpaces />

            <ng-container aplzFormError>
              @if (nameInput.touched && nameInput.invalid) {
                @if (nameInput.getError('required')) {
                  <p>
                    {{ context.textUI.errors.required }}
                  </p>
                }

                @if (nameInput.getError('minlength')) {
                  <p>
                    {{ context.textUI.errors.minlength }}
                  </p>
                }
              }
            </ng-container>
          </aplz-ui-form-field>

          <aplz-ui-form-field>
            <aplz-ui-form-label>
              {{ context.textUI.form.phone }}
            </aplz-ui-form-label>
            <input
              type="text"
              aplzFormInput
              inputmode="tel"
              name="phone"
              [ngModel]="phone()"
              (ngModelChange)="phone.set($event)"
              required
              [minlength]="phoneMinLength"
              ngModel
              #phoneInput="ngModel"
              data-test="phone-input"
              aplazoOnlyNumbers
              [aplazoTruncateLength]="phoneMinLength" />

            <ng-container aplzFormError>
              @if (phoneInput.touched && phoneInput.invalid) {
                @if (phoneInput.getError('required')) {
                  <p>
                    {{ context.textUI.errors.required }}
                  </p>
                }

                @if (phoneInput.getError('minlength')) {
                  <p>
                    {{ context.textUI.errors.phoneLength }}
                  </p>
                }
              }
            </ng-container>
          </aplz-ui-form-field>
        </div>

        <aplz-ui-form-field>
          <aplz-ui-form-label>
            {{ context.textUI.form.comments }}
          </aplz-ui-form-label>
          <textarea
            aplzFormInput
            name="comments"
            [ngModel]="comments()"
            (ngModelChange)="comments.set($event)"
            rows="5"
            ngModel
            data-test="comments-input"
            #commentsInput="ngModel">
          </textarea>
        </aplz-ui-form-field>

        <div class="my-10">
          <button
            aplzButton
            type="submit"
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            data-test="submit-button"
            [rounded]="true">
            {{ context.textUI.form.submit }}
          </button>
        </div>
      </form>
    </aplz-ui-card>
  </section>
}
