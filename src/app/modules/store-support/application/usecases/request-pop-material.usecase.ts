import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  combineLatestWith,
  finalize,
  map,
  Observable,
  switchMap,
  take,
  tap,
  throwError,
} from 'rxjs';
import { StoreService } from 'src/app/core/application/services/store.service';
import { StorefrontHelpWithHttpRepository } from '../../infra/repositories/storefront-help-with-http.repository';

export interface PopMaterialUIRequest {
  name: string;
  phone: string;
  comments?: string;
  hasTentCard?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class RequestPopMaterialUsecase
  implements BaseUsecase<PopMaterialUIRequest, Observable<void>>
{
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #repository = inject(StorefrontHelpWithHttpRepository);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #store = inject(StoreService);

  execute(req: PopMaterialUIRequest): Observable<void> {
    const idLoader = this.#loader.show();

    try {
      if (!Guard.againstNullOrUndefined(req, 'name').succeeded) {
        throw new RuntimeMerchantError(
          'El nombre es requerido',
          'RequestPopMaterialUsecase::empty::name'
        );
      }

      if (!Guard.againstNullOrUndefined(req, 'phone').succeeded) {
        throw new RuntimeMerchantError(
          'El teléfono es requerido',
          'RequestPopMaterialUsecase::empty::phone'
        );
      }

      Guard.againstInvalidNumbers(
        +req.phone,
        'RequestPopMaterialUsecase',
        'El teléfono no es válido'
      );

      return this.#store.getMerchantId$().pipe(
        combineLatestWith(this.#store.selectedBranch$.pipe(map(b => b?.id))),
        switchMap(([merchantId, storefrontId]) => {
          return this.#repository.requestPopMaterial({
            name: req.name,
            phone: req.phone,
            merchantId: merchantId ? String(merchantId) : '',
            storefrontId: storefrontId ? String(storefrontId) : '',
            comments: req.comments,
            hasTentCard: req.hasTentCard,
          });
        }),
        catchError(e => this.#errorHandler.handle<never>(e)),
        tap(() => {
          this.#notifier.success({
            title: 'Hemos recibido tu solicitud.',
            message:
              'Pronto nos pondremos en contacto contigo para coordinar la entrega del material.',
          });
        }),
        finalize(() => {
          this.#loader.hide(idLoader);
        }),
        take(1)
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
