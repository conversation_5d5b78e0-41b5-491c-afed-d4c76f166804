import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { Component, OnDestroy, OnInit, inject } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterModule,
} from '@angular/router';
import { I18NService } from '@aplazo/i18n';
import { NotifierService, RedirectionService } from '@aplazo/merchant/shared';
import { AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDashboardComponents } from '@aplazo/shared-ui/dashboard';
import { AplazoDetailsComponents } from '@aplazo/shared-ui/details';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoConfirmDialogComponent } from '@aplazo/shared-ui/merchant';
import { AplazoSidenavLinkComponent } from '@aplazo/shared-ui/sidenav';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import {
  iconBlueBellIndicator,
  iconGift,
  iconHome,
  iconMenu1,
  iconOpenBook,
  iconPricetag,
  iconSettings,
} from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigModule, StatsigService } from '@statsig/angular-bindings';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  combineLatestWith,
  distinctUntilChanged,
  filter,
  map,
  of,
  scan,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
  timer,
} from 'rxjs';
import { INotificationsWithNewOnesUIDto } from 'src/app/core/domain/notification';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import { StoreService } from '../../core/application/services/store.service';
import { POS_APP_ROUTES } from '../../core/domain/config/app-routes.core';
import { NotificationsHandlerService } from '../../core/services/notifications-handler.service';
import { NotificationsStore } from '../../core/services/notifications-store.service';
import { SecuredV1LogoutUsecase } from './secured-v1-logout.usecase';
interface ILogoutDialogInjectedTextUI {
  title: string;
  cancelButton: string;
  acceptButton: string;
  dynamicMessage?: string;
}

@Component({
  selector: 'aplazo-secured-v1',
  standalone: true,
  templateUrl: './secured-v1.component.html',
  styleUrls: ['./secured-v1.component.scss'],
  imports: [
    RouterModule,
    AsyncPipe,
    NgIf,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoTooltipDirective,
    AplazoDropdownComponents,
    AplazoDashboardComponents,
    AplazoSidenavLinkComponent,
    AplazoDetailsComponents,
    StatsigModule,
  ],
})
export class SecuredV1Component implements OnInit, OnDestroy {
  readonly #matchMedia = inject(AplazoMatchMediaService);
  readonly #redirectionService = inject(RedirectionService);
  readonly #storeService = inject(StoreService);
  readonly #dialog = inject(DialogService);
  readonly #logoutUseCase = inject(SecuredV1LogoutUsecase);
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly #i18n = inject(I18NService);
  readonly #notificationsStore = inject(NotificationsStore);
  readonly #notificationHandler = inject(NotificationsHandlerService);
  readonly #notifier = inject(NotifierService);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);
  readonly #registryIconService = inject(AplazoIconRegistryService);
  readonly #statsig = inject(StatsigService);
  readonly #analyticsService = inject(AnalyticsService);
  readonly appRoutes = inject(POS_APP_ROUTES);
  readonly #scope = 'principal-layout';

  readonly ordersV2Enabled = this.#statsig.checkGate(
    'b2b_front_posui_sse_enabled'
  );

  readonly layoutInjectedText$ = this.#i18n
    .getTranslateByKey<string>({
      key: 'logoutButton',
      scope: this.#scope,
    })
    .pipe(map(text => ({ logoutButton: text })));

  readonly logoutDialogInjectedText$ =
    this.#i18n.getTranslateObjectByKey<ILogoutDialogInjectedTextUI>({
      key: 'logoutDialog',
      scope: this.#scope,
    });
  readonly buttonsText$ = this.#i18n.getTranslateObjectByKey<{
    logout: string;
  }>({
    key: 'buttons',
    scope: this.#scope,
  });

  readonly menuText$ = this.#i18n
    .getTranslateObjectByKey<{
      labels: {
        orders: {
          main: string;
          v1: string;
          v2: string;
          historical: string;
        };
        challenges: {
          main: string;
        };
        notifications: {
          main: string;
        };
        premios: {
          main: string;
        };
        aplazoVersity: {
          main: string;
        };
        storeSupport: {
          main: string;
          training: string;
          popMaterials: string;
        };
      };
    }>({
      key: 'sidebar',
      scope: this.#scope,
    })
    .pipe(map(text => text.labels));

  readonly #headerTitlesText$ = this.#i18n
    .getTranslateObjectByKey<{
      titles: {
        orders: string;
        challenges: string;
        notifications: string;
        premios: string;
        aplazoVersity: string;
        ordersV2: string;
        historical: string;
        cart: string;
        preloan: string;
        storeSupport: string;
      };
    }>({
      key: 'header',
      scope: this.#scope,
    })
    .pipe(map(text => text.titles));

  readonly #destroy$: Subject<void> = new Subject<void>();

  readonly bannerFlag$ = this.#storeService.getMerchantId$().pipe(
    map(() => {
      const enabled = this.#statsig.checkGate('b2b_front_posui_banner');
      return {
        enabled,
        dynamicText: {
          addedCreditLimit: '$2,500',
          discountAmount: '$3,500',
        },
      };
    }),
    switchMap(({ enabled, dynamicText }) =>
      this.#i18n
        .getTranslateObjectByKey<{
          content: string;
        }>({
          key: 'banner',
          scope: this.#scope,
          params: {
            content: {
              addedCreditLimit: dynamicText?.addedCreditLimit,
              discountAmount: dynamicText?.discountAmount,
            },
          },
        })
        .pipe(
          map(text => ({
            enabled,
            text: text?.content ?? '',
          }))
        )
    )
  );

  isLargeScreen$ = this.#matchMedia.matchLgScreen$;

  title$: Observable<string> = this.#getTitle();

  name$ = this.#storeService.merchantName$;

  branchName$: Observable<string | null> =
    this.#storeService.selectedBranchName$;

  readonly #errorImage =
    'https://aplazoassets.s3.us-west-2.amazonaws.com/merchant-dash-assets/settings.svg';

  readonly #imageHasError$ = new BehaviorSubject<boolean>(false);
  readonly img$ = combineLatest([
    this.#storeService.merchantLogoUrl$.pipe(
      map(logoUrl => logoUrl || ''),
      tap(() => {
        this.#imageHasError$.next(false);
      })
    ),
    this.#imageHasError$.asObservable(),
  ]).pipe(
    map(([imgUrl, hasError]) => {
      if (hasError) {
        return this.#errorImage;
      }
      return imgUrl;
    }),
    distinctUntilChanged()
  );

  readonly isSellAgentRole$ = this.#storeService.userRole$.pipe(
    map(role => role.toLowerCase() === 'role_sell_agent')
  );

  readonly hasNewPromos$ = this.#notificationsStore.hasNewNotifications$();

  readonly allNewNotifications$ =
    this.#notificationsStore.newNotificationsCounter$();

  readonly vm$ = combineLatest({
    banner: this.bannerFlag$,
    isLargeScreen: this.isLargeScreen$,
    title: this.title$,
    name: this.name$,
    branchName: this.branchName$,
    isSellAgentRole: this.isSellAgentRole$,
    hasNewPromos: this.hasNewPromos$,
    allNewNotifications: this.allNewNotifications$,
    buttons: this.buttonsText$,
    menu: this.menuText$,
  });

  constructor() {
    this.#registryIconService.registerIcons([
      iconMenu1,
      iconHome,
      iconPricetag,
      iconOpenBook,
      iconSettings,
      iconBlueBellIndicator,
      iconGift,
    ]);
  }

  openDocument(url: string): void {
    this.#redirectionService.openPdf(url);
  }

  goToMerchantLandingRoute(): void {
    this.#redirectionService.externalNavigation(
      `${this.#environment.landingpage}para-comercios`,
      '_blank'
    );
  }

  goToFAQLandingRoute(): void {
    this.#redirectionService.externalNavigation(
      `${this.#environment.landingpage}preguntas-frecuentes`,
      '_blank'
    );
  }

  goToMerchantWhatsapp(): void {
    this.#redirectionService.externalNavigation(
      'https://api.whatsapp.com/send/?phone=525570058799',
      '_blank'
    );
  }

  goToNotificationsRoute(): void {
    this.#redirectionService.internalNavigation([
      this.appRoutes.aplazoRoot,
      this.appRoutes.aplazoLayout,
      this.appRoutes.aplazoNotifications,
    ]);
  }

  logoClicked(): void {
    this.#redirectionService.internalNavigation('/');
  }

  onLoadError(): void {
    this.#imageHasError$.next(true);
  }

  logout(): void {
    this.logoutDialogInjectedText$
      .pipe(
        take(1),
        switchMap(text =>
          this.#dialog
            .open(AplazoConfirmDialogComponent, {
              data: text,
              maxWidth: '320px',
            })
            .afterClosed$.pipe(
              take(1),
              tap(hasLogoutConfirmation => {
                if (hasLogoutConfirmation?.confirmation) {
                  this.#logoutUseCase.execute();
                }
              })
            )
        )
      )
      .subscribe();
  }

  trackSidebarClick(
    buttonName: string,
    timestamp = new Date().getTime()
  ): void {
    this.#analyticsService.track('buttonClick', {
      buttonName: buttonName,
      timestamp: timestamp,
      url: this.#router.url,
    });
  }

  ngOnInit(): void {
    const baseMiliseconds = 1000;
    const dueDate =
      // because is the one that warranty the correct environment
      this.#environment.datadogEnv === 'stage'
        ? baseMiliseconds * 15
        : baseMiliseconds * 60 * 30;

    timer(0, dueDate)
      .pipe(
        takeUntil(this.#destroy$),
        switchMap(() => this.#notificationHandler.getNotifications$()),
        scan((previous, current) => {
          const allPreviousNewIds = previous.flatMap(n => n.newOnesIds);
          const allCurrentNewIds = current.flatMap(n => n.newOnesIds);

          const newOnes = allCurrentNewIds.filter(
            id => !allPreviousNewIds.includes(id)
          );

          if (newOnes.length > 0) {
            this.#notifier.info({
              title: 'Tienes notificaciones nuevas o no leídas',
            });
          }

          return current;
        }, [] as INotificationsWithNewOnesUIDto[]),
        tap(nots => {
          this.#notificationsStore.setNotifications(nots);
        })
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #getTitle(): Observable<string> {
    return this.#router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      switchMap(() => {
        if (this.#route.firstChild) {
          return this.#route.firstChild.data.pipe(map(data => data.i18nTitle));
        }
        return of('');
      }),
      startWith(this.#route.snapshot.firstChild?.data?.i18nTitle || ''),
      combineLatestWith(this.#headerTitlesText$.pipe(take(1))),
      map(
        ([title, titles]: [
          string,
          {
            orders: string;
            challenges: string;
            notifications: string;
            premios: string;
            aplazoVersity: string;
            ordersV2: string;
            historical: string;
            cart: string;
            preloan: string;
          },
        ]) => titles[title as keyof typeof titles] || ''
      )
    );
  }
}
