<ng-container *ngIf="vm$ | async as context">
  <aplz-ui-dashboard (logoClick)="logoClicked()">
    <p
      aplzDashboardBanner
      [hidden]="context.banner.enabled === false"
      [aplzTooltip]="context.banner.text">
      {{ context.banner.text }}
    </p>

    <aplz-ui-dash-header>
      <nav
        class="flex flex-grow flex-shrink-0 justify-between items-center w-full">
        <h2 class="font-medium text-2xl">
          {{ context.title }}
        </h2>

        <div
          class="flex-grow flex-shrink-0 flex items-center justify-end font-light text-base mx-3"
          *ngIf="context.isLargeScreen?.matches === true">
          <button
            aplzButton
            aplzAppearance="basic"
            aplzColor="light"
            size="sm"
            *ngIf="context.hasNewPromos === true"
            (click)="goToNotificationsRoute()">
            <aplz-ui-icon name="blue-bell-indicator" size="sm"></aplz-ui-icon>
            <span class="sr-only">bell indicator</span>
          </button>
          <span
            *ngIf="context.name"
            class="max-w-[22ch] truncate"
            [aplzTooltip]="context.name">
            {{ context.name }}
          </span>
          <span
            class="inline-block w-0.5 h-5 bg-dark-tertiary mx-2"
            aria-hidden="true">
          </span>
          <span
            class="max-w-[22ch] truncate"
            *ngIf="context.branchName"
            [aplzTooltip]="context.branchName">
            {{ context.branchName }}
          </span>
        </div>

        <button
          aplzButton
          aplzAppearance="basic"
          size="xs"
          [aplzDropdownTriggerFor]="logoutMenu">
          <span class="image-container" *ngIf="img$ | async as img; else iconM">
            <img [src]="img" alt="logo del comercio" (error)="onLoadError()" />
          </span>
        </button>
        <aplz-ui-dropdown #logoutMenu>
          <aplz-ui-dropdown-item>
            <button class="py-2 px-4 w-full" (click)="logout()">
              {{ context.buttons.logout }}
            </button>
          </aplz-ui-dropdown-item>
        </aplz-ui-dropdown>
      </nav>
    </aplz-ui-dash-header>
    <aplz-ui-dash-sidebar>
      <aplz-ui-details>
        <summary aplzDetailsHeader>
          <div class="flex">
            <aplz-ui-icon name="home" size="sm"></aplz-ui-icon>
            <span class="ml-7">{{ context.menu.orders.main }}</span>
          </div>
        </summary>
        @if (ordersV2Enabled) {
          <a
            aplzSidenavLink
            [routerLink]="[appRoutes.securedOrdersV2]"
            (click)="trackSidebarClick('sidebarToday')">
            {{ context.menu.orders.v2 }}
          </a>
        } @else {
          <a
            aplzSidenavLink
            [routerLink]="[appRoutes.securedOrders]"
            (click)="trackSidebarClick('sidebarToday')">
            {{ context.menu.orders.v1 }}
          </a>
        }
        <a
          aplzSidenavLink
          [routerLink]="[appRoutes.securedHistoric]"
          (click)="trackSidebarClick('sidebarHistorical')">
          {{ context.menu.orders.historical }}
        </a>
      </aplz-ui-details>

      <a
        aplzSidenavLink
        *stgCheckGate="'b2b_front_posui_challenges'"
        [routerLink]="appRoutes.challenges"
        (click)="trackSidebarClick('sidebarChallenges')"
        iconName="gift">
        {{ context.menu.challenges.main }}
      </a>
      <a
        aplzSidenavLink
        [routerLink]="[appRoutes.aplazoNotifications]"
        (click)="trackSidebarClick('sidebarNotifications')"
        iconName="pricetag">
        <span
          class="inline-flex items-start gap-2"
          [class.font-bold]="context.allNewNotifications > 0"
          [class.text-dark-black]="context.allNewNotifications > 0">
          {{ context.menu.notifications.main }}

          <span
            *ngIf="context.allNewNotifications > 0"
            class="relative inline-flex items-center justify-center w-6 aspect-square font-bold bg-transparent before:absolute before:rounded-full before:bg-special-danger before:w-full before:h-full before:z-[-1] z-[1] text-light text-[12px] flex-shrink-0 flex-grow-0">
            {{ context.allNewNotifications }}
          </span>
        </span>
      </a>
      <a
        aplzSidenavLink
        [routerLink]="[appRoutes.contestEntries]"
        (click)="trackSidebarClick('sidebarContestEntries')"
        iconName="gift">
        {{ context.menu.premios.main }}
      </a>

      <aplz-ui-details>
        <summary aplzDetailsHeader>
          <div class="flex">
            <aplz-ui-icon name="pricetag" size="sm"></aplz-ui-icon>
            <span class="ml-7">{{ context.menu['storeSupport'].main }}</span>
          </div>
        </summary>
        <a
          aplzSidenavLink
          [routerLink]="[appRoutes.storeSupport, 'training']"
          (click)="trackSidebarClick('sidebarStoreSupportTraining')">
          {{ context.menu['storeSupport'].training }}
        </a>
        <a
          aplzSidenavLink
          [routerLink]="[appRoutes.storeSupport, 'pop-materials']"
          (click)="trackSidebarClick('sidebarStoreSupportPopMaterials')">
          {{ context.menu['storeSupport'].popMaterials }}
        </a>
      </aplz-ui-details>

      <a
        aplzSidenavLink
        *stgCheckGate="'b2b_front_posui_aplazoversity'"
        [routerLink]="[appRoutes.aplazoContentMedia]"
        (click)="trackSidebarClick('sidebarAplazoVersity')"
        iconName="open-book">
        {{ context.menu.aplazoVersity.main }}
      </a>
    </aplz-ui-dash-sidebar>

    <router-outlet></router-outlet>
  </aplz-ui-dashboard>
</ng-container>

<ng-template #iconM>
  <aplz-ui-icon name="menu-1" size="sm"></aplz-ui-icon>
</ng-template>
