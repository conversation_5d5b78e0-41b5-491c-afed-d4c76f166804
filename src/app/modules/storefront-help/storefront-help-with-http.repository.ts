import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from 'src/app-core/domain/environments';

export interface PopMaterialRequest {
  name: string;
  phone: string;
  merchantId: string;
  storefrontId: string;
  comments?: string;
  hasTentCard?: boolean;
}

export interface TrainingRequest {
  trainingType: string;
  contactName: string;
  contactPhone: string;
  merchantId: string;
  storefrontId: string;
  additionalComments?: string;
}

@Injectable({
  providedIn: 'root',
})
export class StorefrontHelpWithHttpRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);
  readonly #baseUrl = `${this.#environment.exposedPublicApiUrl}/storefront-help`;

  requestPopMaterial(data: PopMaterialRequest): Observable<void> {
    console.log(data);
    // return throwError(
    //   () =>
    //     new HttpErrorResponse({
    //       status: 400,
    //       error: 'Error al solicitar el material',
    //       statusText: 'Bad Request',
    //     })
    // );
    return of(void 0);
  }

  requestTraining(data: TrainingRequest): Observable<void> {
    console.log('Training request:', data);
    // TODO: Implement actual HTTP call to /store-support/requests endpoint
    // return this.#http.post<void>(`${this.#baseUrl}/requests`, data);
    return of(void 0);
  }
}
