import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, take, throwError } from 'rxjs';
import {
  raffleRequestFromUIToRepo,
  raffleResponseToUI,
  RaffleTicketsRequestUI,
  RaffleTicketsUI,
} from '../../domain/raffle';
import { RaffleRepository } from '../../domain/repositories/raffle.repository';

@Injectable()
export class GetRaffleUsecase
  implements BaseUsecase<RaffleTicketsRequestUI, Observable<RaffleTicketsUI>>
{
  readonly #repository = inject(RaffleRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: RaffleTicketsRequestUI): Observable<RaffleTicketsUI> {
    const idLoader = this.#loader.show();

    try {
      if (!Guard.againstNullOrUndefined(args, 'participantId').succeeded) {
        throw new RuntimeMerchantError(
          'El código del participante es requerido',
          'GetRaffleUsecase::emptyParticipantCode'
        );
      }

      const request = raffleRequestFromUIToRepo(args);

      return this.#repository.getRaffle(request).pipe(
        map(raffleResponseToUI),
        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status === 404 &&
            err.error.code === 'APZRWS009'
          ) {
            throw new RuntimeMerchantError(
              'El código del participante no existe',
              'GetRaffleUsecase::participantNotFound'
            );
          }

          return this.#errorHandler.handle<never>(err);
        }),

        take(1),

        finalize(() => this.#loader.hide(idLoader))
      );
    } catch (error) {
      this.#loader.hide(idLoader);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
