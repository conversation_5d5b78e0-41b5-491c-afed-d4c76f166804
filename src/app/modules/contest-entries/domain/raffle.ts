import {
  Guard,
  PageableResponse,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';

export interface RaffleTicketsUI {
  data: RaffleTicket[];
  participantId: string;
  participantName?: string;
  totalItems: number;
  totalPages: number;
}

export interface RaffleTicketsResponse extends PageableResponse {
  content?: RaffleTicket[];
}

export interface RaffleTicket {
  id: number;
  participantName: string;
  participantId: string;
  ticketNumber: string;
  assignationType: string;
  campaignId: string;
  createdAt: string; // string date no timezone specified
}

export interface SortInfo {
  direction?: string;
  property?: string;
  ignoreCase?: boolean;
  nullHandling?: string;
  descending?: boolean;
  ascending?: boolean;
}

export interface RaffleTicketsRequestUI {
  participantId: string;
  page: number;
}

export interface RaffleTicketsRequest {
  participantId: string;
  page: number;
  sortBy: 'ID' | 'CAMPAIGNID' | 'TYPE' | 'CREATEDAT';
  sortDirection: 'asc' | 'desc';
}

export const raffleRequestFromUIToRepo = (
  request: RaffleTicketsRequestUI
): RaffleTicketsRequest => {
  if (!Guard.againstEmptyValue(request, 'participantId').succeeded) {
    throw new RuntimeMerchantError(
      'El código del participante es requerido',
      'GetRaffleUsecase::emptyParticipantCode'
    );
  }

  if (!Guard.againstNullOrUndefined(request, 'page').succeeded) {
    throw new RuntimeMerchantError(
      'La página es requerida',
      'GetRaffleUsecase::emptyPage'
    );
  }

  Guard.againstInvalidNumbers(
    +request.page,
    'raffleRequestFromUIToRepo::invalidPage',
    'La página debe ser un número válido'
  );

  if (request.page < 0) {
    throw new RuntimeMerchantError(
      'La página debe ser mayor a 0',
      'GetRaffleUsecase::invalidPage'
    );
  }

  return {
    participantId: request.participantId,
    page: request.page,
    sortBy: 'CREATEDAT',
    sortDirection: 'desc',
  };
};

export const raffleResponseToUI = (
  response: RaffleTicketsResponse
): RaffleTicketsUI => {
  return {
    data: response.content ?? [],
    participantId: response.content?.[0]?.participantId ?? '',
    participantName: response.content?.[0]?.participantName ?? '',
    totalItems: response.totalElements ?? 0,
    totalPages: response.totalPages ?? 0,
  };
};
