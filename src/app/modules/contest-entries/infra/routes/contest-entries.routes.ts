import { inject } from '@angular/core';
import { Route } from '@angular/router';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { GetAllContestsRegions } from '../../application/usecases/retrieve-regions.usecase';
import {
  provideContest,
  provideRaffle,
  withScopedLoader,
} from '../config/providers';
import { AplazoContestEntriesLayoutComponent } from '../layout/contest-entries.layout.component';
import { AplazoContestPositionComponent } from '../pages/contest-position/contest-position.component';
import { AplazoContestRegistrationComponent } from '../pages/contest-registration/contest-registration.component';
import { RaffleCampaignComponent } from '../pages/raffle-campaign/raffle-campaign.component';

const contestRoutes: Route[] = [
  {
    path: '',
    component: AplazoContestEntriesLayoutComponent,
    providers: [
      provideContest(withScoped<PERSON>oader()),
      provideRaffle(withScoped<PERSON>oader()),
    ],
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: ROUTE_CONFIG.contestRegistration,
      },
      {
        path: ROUTE_CONFIG.contestRegistration,
        component: AplazoContestRegistrationComponent,
        resolve: {
          regions: () => inject(GetAllContestsRegions).execute(),
        },
      },
      {
        path: ROUTE_CONFIG.contestPosition,
        component: AplazoContestPositionComponent,
      },
      {
        path: ROUTE_CONFIG.raffleCampaign,
        component: RaffleCampaignComponent,
      },
    ],
  },
];

export default contestRoutes;
