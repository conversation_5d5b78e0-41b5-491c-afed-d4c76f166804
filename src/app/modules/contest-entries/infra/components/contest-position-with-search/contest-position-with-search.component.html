@if ((hasDetails$ | async) === false) {
  <article
    class="py-8 px-6 grid justify-center w-full font-light text-dark-secondary text-lg">
    <div class="max-w-lg">
      <h2 class="text-center text-dark-secondary text-2xl font-medium">
        ¡Consulta tu progreso!
      </h2>
      <p
        class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
        Mantente al tanto de tu progreso. Ingresa tu y accede a tu posición y
        herramientas exclusivas.
      </p>

      <p
        class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
        Ingresa tu
        <span class="mx-1 font-medium"> código de participante </span>
        para saber tu posición en la tabla de posiciones.
      </p>

      <p
        class="mt-6 leading-8 tracking-normal font-medium text-dark-secondary text-pretty">
        ¿Cómo funciona?
      </p>

      <ol
        class="leading-8 tracking-normal font-light text-dark-secondary text-pretty list-decimal list-inside">
        <li>Encuentra tu código único (Ej. AFIK1234).</li>
        <li>Escríbelo en el campo de búsqueda.</li>
        <li>Haz clic en consultar mi posición para ver tu posición actual.</li>
      </ol>

      <p
        class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
        <span class="font-medium">¡Mantente en el top de la tabla</span>
        y consigue increíbles premios!
      </p>
    </div>

    <app-participant-code
      [textUI]="textUI"
      [(participantCode)]="search"
      (participantCodeEvent)="retrieveParticipantInfo()"></app-participant-code>
  </article>
} @else {
  <div
    class="px-8 mt-8 flex justify-center md:justify-between items-center flex-wrap gap-4">
    <button
      aplzButton
      (click)="clear()"
      aplzAppearance="stroked"
      aplzColor="aplazo"
      size="md"
      class="flex-grow-0 flex-shrink-0">
      <aplz-ui-icon name="arrow-left" size="md"></aplz-ui-icon>
      <span class="ml-2">Cambiar participante</span>
    </button>

    <p
      class="text-xl text-dark-secondary leading-8 tracking-normal font-light text-right">
      Código Participante:
      <span class="ml-1 font-medium text-lg">
        {{ search() }}
      </span>
    </p>
  </div>
}
