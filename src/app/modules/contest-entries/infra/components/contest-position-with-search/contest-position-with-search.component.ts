import { AsyncPipe } from '@angular/common';
import { Component, inject, OnD<PERSON>roy, signal } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconArrowLeft } from '@aplazo/ui-icons';
import { StatsigService } from '@statsig/angular-bindings';
import { catchError, lastValueFrom, of, take } from 'rxjs';
import { AnalyticsService } from '../../../../../core/application/services/analytics.service';
import { DynamicErrorService } from '../../../../shared/participant-code/dynamic-error.service';
import {
  ParticipantCodeComponent,
  ParticipantCodeTextUI,
  participantErrorKey,
} from '../../../../shared/participant-code/participant-code.component';
import { GetOneParticipantWithDetailsUseCase } from '../../../application/usecases/get-one-with-details.usecase';
import { ContestStoreService } from '../../services/contest-store.service';

@Component({
  standalone: true,
  selector: 'app-contest-position-with-search',
  templateUrl: './contest-position-with-search.component.html',
  imports: [
    AsyncPipe,
    AplazoButtonComponent,
    AplazoIconComponent,
    ParticipantCodeComponent,
  ],
})
export class AplazoContestPositionWithSearchComponent implements OnDestroy {
  readonly #getOneUseCase = inject(GetOneParticipantWithDetailsUseCase);
  readonly #store = inject(ContestStoreService);
  readonly #analytics = inject(AnalyticsService);
  readonly #flags = inject(StatsigService);
  readonly #dynamicErrorService = inject(DynamicErrorService);
  readonly #iconRegister = inject(AplazoIconRegistryService);

  readonly textUI: ParticipantCodeTextUI = {
    errors: {
      required: 'Ingrese un código de participante',
      minlength: 'Ingrese un código de al menos 3 caracteres',
      participantNotFound: 'Código de participante no encontrado',
    },
    hints: {
      default: 'Código de participante. Ej. AFIK1234',
    },
    labels: {
      submit: 'Consultar mi posición',
      placeholder: 'abcd0000',
    },
  };

  readonly search = signal<string | null>(null);

  readonly hasDetails$ = this.#store.hasDetails$;

  constructor() {
    this.#iconRegister.registerIcons([iconArrowLeft]);
  }

  clear(): void {
    this.search.set(null);
    this.#store.clearRanking();
  }

  async retrieveParticipantInfo(): Promise<void> {
    const data = this.search();

    if (!data) {
      return;
    }

    try {
      this.#analytics.track('buttonClick', {
        timestamp: new Date().getTime(),
        label: 'searchPosition',
        searchTerm: data,
      });

      const result = await lastValueFrom(
        this.#getOneUseCase.execute(data).pipe(
          catchError(err => {
            const isControlledError = err instanceof RuntimeMerchantError;

            this.#flags.logEvent('lpa_front_get_ranking_error', data, {
              search_term: data,
              error: err.message ?? '',
              error_code: err.code ?? '',
              error_name: err.status_code ?? '',
            });

            if (
              isControlledError &&
              err.code === 'GetOneWithDetailsUseCase::participantNotFound'
            ) {
              this.#dynamicErrorService.dynamicErrors.update(currentErrors => {
                const errors = currentErrors ?? {};
                errors[data] = {
                  [participantErrorKey]: err.message,
                };
                return errors;
              });
            }

            return of(null);
          }),
          take(1)
        )
      );

      if (result) {
        this.#store.setNewRanking(result);

        this.#flags.logEvent('lpa_front_get_ranking_success', data, {
          search_term: data,
          finish_date: result.campaignFinishDate ?? '',
          participant_id: result.participantId ?? '',
          tier: result.tier?.toString() ?? '',
          total_registration:
            result.data?.[0]?.totalRegistration?.toString() ?? '',
        });
      }
    } catch (error) {
      console.warn(error);
    }
  }

  ngOnDestroy(): void {
    this.clear();
  }
}
