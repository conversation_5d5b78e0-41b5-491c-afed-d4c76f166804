import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgFor } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterOutlet,
} from '@angular/router';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { delay, filter, map, startWith } from 'rxjs';
import { AnalyticsService } from '../../../../core/application/services/analytics.service';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';

export const contestLabelRoutes = {
  Inscripción: ROUTE_CONFIG.contestRegistration,
  'Mi Posición': ROUTE_CONFIG.contestPosition,
  'Mis Boletos': ROUTE_CONFIG.raffleCampaign,
} as const;

@Component({
  standalone: true,
  selector: 'app-layout-contest-entries',
  template: `
    <section class="min-h-[90vh] px-2 md:px-8 pt-2 md:pt-8 pb-[30vh]">
      <div class="block border border-dark-background rounded-lg md:p-8">
        <aplz-ui-tab-group
          [selectedIndex]="selectedIndex$ | async"
          (tabSelectionChange)="changeTab($event)">
          <aplz-ui-tab *ngFor="let label of links" [label]="label">
          </aplz-ui-tab>
        </aplz-ui-tab-group>
        <div>
          <router-outlet></router-outlet>
        </div>
      </div>
    </section>
  `,
  imports: [AplazoTabsComponents, RouterOutlet, NgFor, AsyncPipe],
})
export class AplazoContestEntriesLayoutComponent {
  readonly #router = inject(Router);
  readonly #route = inject(ActivatedRoute);
  readonly #analytics = inject(AnalyticsService);
  readonly links = Object.keys(contestLabelRoutes);

  readonly selectedIndex$ = this.#router.events.pipe(
    filter(event => event instanceof NavigationEnd),
    startWith(this.#router),
    delay(0),
    map(() => {
      const currentUrl = this.#router.url;
      const currentTab = Object.entries(contestLabelRoutes).find(([, path]) =>
        currentUrl.endsWith(path)
      );

      if (currentTab) {
        const [label] = currentTab;
        const index = this.links.indexOf(label);

        if (index < 0) {
          return 0;
        }

        this.#analytics.track('tabSelection', {
          category: 'premios_aplazo',
          label: label,
          timestamp: new Date().getTime(),
        });

        return index;
      }

      return 0;
    })
  );

  changeTab(event: { index: number }): void {
    const selectedTab = this.links[
      event.index
    ] as keyof typeof contestLabelRoutes;
    const finalPath = contestLabelRoutes[selectedTab];

    this.#router.navigate([finalPath], { relativeTo: this.#route });
  }
}
