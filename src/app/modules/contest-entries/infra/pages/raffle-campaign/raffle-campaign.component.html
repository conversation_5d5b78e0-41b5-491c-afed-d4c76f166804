<div class="pt-20 max-w-4xl mx-auto">
  @if (raffle$ | async; as raffle) {
    @if (raffle.totalItems === 0) {
      <article
        class="py-8 px-6 grid justify-center w-full font-light text-dark-secondary text-lg">
        <div class="max-w-lg">
          <h2 class="text-center text-dark-secondary text-2xl font-medium">
            ¡Consulta tus boletos!
          </h2>
          <p
            class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
            Mantente al tanto de tu progreso.
          </p>

          <p
            class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
            Ingresa tu
            <span class="mx-1 font-medium"> ID de participante </span>
            para saber tu participación en el sorteo.
          </p>

          <p
            class="mt-6 leading-8 tracking-normal font-medium text-dark-secondary text-pretty">
            ¿Cómo funciona?
          </p>

          <ol
            class="leading-8 tracking-normal font-light text-dark-secondary text-pretty list-decimal list-inside">
            <li>Encuentra tu código único (Ej. AFIK1234).</li>
            <li>Escríbelo en el campo de búsqueda.</li>
            <li>
              Haz clic en consultar mis boletos para ver tu participación.
            </li>
          </ol>
        </div>

        <app-participant-code
          [textUI]="textUI"
          [(participantCode)]="search"
          (participantCodeEvent)="
            retrieveParticipantInfo()
          "></app-participant-code>
      </article>
    } @else {
      <div
        class="px-8 mt-8 flex justify-center md:justify-between items-center flex-wrap gap-4">
        <button
          aplzButton
          (click)="clear()"
          aplzAppearance="stroked"
          aplzColor="aplazo"
          size="md"
          class="flex-grow-0 flex-shrink-0">
          <aplz-ui-icon name="arrow-left" size="md"></aplz-ui-icon>
          <span class="ml-2">Salir</span>
        </button>

        <p
          class="text-xl text-dark-secondary leading-8 tracking-normal font-light text-right">
          @if (raffle.participantName) {
            Nombre del participante:
            <span class="ml-1 font-medium text-lg">
              {{ raffle.participantName }}
            </span>
          } @else {
            Código Participante:
            <span class="ml-1 font-medium text-lg">
              {{ search() }}
            </span>
          }
        </p>
      </div>

      <section class="py-12">
        @if (raffle.data.length > 0) {
          <h2
            class="text-lg text-dark-primary text-center font-light max-w-32 mx-auto">
            Total de boletos:
            <span class="font-semibold mx-2">
              {{ raffle.data.length }}
            </span>
          </h2>

          <ul
            class="flex flex-col h-full w-auto my-2 items-center overflow-y-auto max-h-96">
            @for (item of raffle.data; track item; let even = $even) {
              <li
                class="p-3 h-18 text-pretty flex-shrink-0 flex-grow flex items-center justify-center tabular-nums w-full max-w-96 text-center truncate"
                [class.bg-white]="!even"
                [class.bg-dark-background]="even">
                {{ item.ticketNumber || item.id }}
              </li>
            }
          </ul>

          <div class="mt-6">
            <aplz-ui-pagination
              [totalPages]="raffle.totalPages"
              [maxPagesToShow]="10"
              [currentPage]="selectedPage()"
              (selectedPage)="changePage($event)">
            </aplz-ui-pagination>
          </div>
        } @else {
          <p class="text-center text-dark-secondary text-lg">
            Aún no tienes boletos asignados. ¡Sigue participando para ganar!
          </p>
        }
      </section>
    }
  }
</div>
