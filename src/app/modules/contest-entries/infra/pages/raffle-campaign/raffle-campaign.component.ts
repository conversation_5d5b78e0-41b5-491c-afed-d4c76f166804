import { Async<PERSON>ipe } from '@angular/common';
import { Component, computed, inject, OnD<PERSON>roy, signal } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoPaginationComponent } from '@aplazo/shared-ui/pagination';
import { iconArrowLeft } from '@aplazo/ui-icons';
import { StatsigService } from '@statsig/angular-bindings';
import { catchError, combineLatest, firstValueFrom, of, take } from 'rxjs';
import { DynamicErrorService } from '../../../../shared/participant-code/dynamic-error.service';
import {
  ParticipantCodeComponent,
  ParticipantCodeTextUI,
  participantErrorKey,
} from '../../../../shared/participant-code/participant-code.component';
import { GetRaffleUsecase } from '../../../application/usecases/get-raffle.usecase';
import { RaffleStoreService } from '../../services/raffle-store.service';

@Component({
  selector: 'app-raffle-campaign',
  standalone: true,
  templateUrl: './raffle-campaign.component.html',
  imports: [
    AsyncPipe,
    AplazoButtonComponent,
    AplazoIconComponent,
    ParticipantCodeComponent,
    AplazoPaginationComponent,
  ],
})
export class RaffleCampaignComponent implements OnDestroy {
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #store = inject(RaffleStoreService);
  readonly #getRaffle = inject(GetRaffleUsecase);
  readonly #flags = inject(StatsigService);
  readonly #dynamicErrorService = inject(DynamicErrorService);

  readonly textUI: ParticipantCodeTextUI = {
    errors: {
      required: 'Ingrese un código de participante',
      minlength: 'Ingrese un código de al menos 3 caracteres',
      participantNotFound: 'Código de participante no encontrado',
    },
    hints: {
      default: 'Código de participante. Ej. AFIK1234',
    },
    labels: {
      submit: 'Consultar mis boletos',
      placeholder: 'abcd0000',
    },
  };

  readonly search = signal<string | null>(null);
  readonly #page = signal<number>(0);

  readonly selectedPage = computed(() => this.#page());

  readonly raffle$ = this.#store.raffle$;

  readonly vm$ = combineLatest({
    raffle: this.raffle$,
  });

  constructor() {
    this.#iconRegister.registerIcons([iconArrowLeft]);
  }

  clear(): void {
    this.search.set(null);
    this.#store.clearRaffle();
  }

  changePage(page: number): void {
    this.#page.set(page);
    this.retrieveParticipantInfo();
  }

  async retrieveParticipantInfo(): Promise<void> {
    if (!this.search()) {
      return;
    }

    try {
      const result = await firstValueFrom(
        this.#getRaffle
          .execute({
            participantId: this.search()!,
            page: this.selectedPage(),
          })
          .pipe(
            catchError(err => {
              const isControlledError = err instanceof RuntimeMerchantError;

              this.#flags.logEvent(
                'lpa_front_get_raffle_error',
                this.search()!,
                {
                  search_term: this.search()!,
                  error: err.message ?? '',
                  error_code: err.code ?? '',
                  error_name: (err as any).status_code ?? err.name ?? '',
                }
              );

              if (
                isControlledError &&
                err.code === 'GetRaffleUsecase::participantNotFound'
              ) {
                this.#dynamicErrorService.dynamicErrors.update(
                  currentErrors => {
                    const errors = currentErrors ?? {};
                    errors[this.search()!] = {
                      [participantErrorKey]: err.message,
                    };
                    return errors;
                  }
                );
              }

              return of(null);
            }),
            take(1)
          )
      );

      if (result) {
        this.#store.setNewRaffle(result);

        this.#flags.logEvent('lpa_front_get_raffle_success', this.search()!, {
          search_term: this.search()!,
          participant_id: result.participantId ?? '',
        });
      }
    } catch (error) {
      console.warn(error);
    }
  }

  ngOnDestroy(): void {
    this.clear();
  }
}
