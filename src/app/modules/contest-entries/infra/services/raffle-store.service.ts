import { Injectable } from '@angular/core';
import { BehaviorSubject, map } from 'rxjs';
import { RaffleTicketsUI } from '../../domain/raffle';

export const defaultRaffle: RaffleTicketsUI = {
  data: [],
  participantId: '',
  totalItems: 0,
  totalPages: 0,
};

@Injectable({ providedIn: 'root' })
export class RaffleStoreService {
  readonly #raffle$ = new BehaviorSubject<RaffleTicketsUI>(defaultRaffle);

  readonly raffle$ = this.#raffle$.pipe();

  readonly hasDetails$ = this.#raffle$.pipe(
    map(({ totalItems }) => (totalItems ?? 0) > 0)
  );

  setNewRaffle(raffle: RaffleTicketsUI): void {
    this.#raffle$.next(raffle);
  }

  clearRaffle(): void {
    this.#raffle$.next(defaultRaffle);
  }
}
