import {
  EnvironmentProviders,
  makeEnvironmentProviders,
  Provider,
} from '@angular/core';
import { LoaderService, LoaderServiceWithQueue } from '@aplazo/merchant/shared';
import { CreateOneParticipantUsecase } from '../../application/usecases/create-one-participant.usecase';
import { GetOneParticipantWithDetailsUseCase } from '../../application/usecases/get-one-with-details.usecase';
import { GetRaffleUsecase } from '../../application/usecases/get-raffle.usecase';
import { GetTermsConditionsLinkUsecase } from '../../application/usecases/get-terms-conditions-link.usecase';
import { GetAllContestsRegions } from '../../application/usecases/retrieve-regions.usecase';
import { SendQRCodeUseCase } from '../../application/usecases/send-qr-code.usecase';
import { ContestRepository } from '../../domain/repositories/contest.repository';
import { RaffleRepository } from '../../domain/repositories/raffle.repository';
import { ContestWithHttpRepository } from '../repositories/contest-with-http.repository';
import { RaffleWithHttpRepository } from '../repositories/raffle-with-http.repository';

export function provideContest(opt?: () => Provider): EnvironmentProviders {
  const providers: Provider[] = [
    {
      provide: ContestRepository,
      useClass: ContestWithHttpRepository,
    },
    GetAllContestsRegions,
    CreateOneParticipantUsecase,
    GetOneParticipantWithDetailsUseCase,
    SendQRCodeUseCase,
    GetTermsConditionsLinkUsecase,
  ];

  if (opt != null && typeof opt === 'function') {
    providers.unshift(opt());
  }

  return makeEnvironmentProviders([...providers]);
}

export function provideRaffle(opt?: () => Provider): EnvironmentProviders {
  const providers: Provider[] = [
    {
      provide: RaffleRepository,
      useClass: RaffleWithHttpRepository,
    },
    GetRaffleUsecase,
  ];

  if (opt != null && typeof opt === 'function') {
    providers.unshift(opt());
  }

  return makeEnvironmentProviders([...providers]);
}

export function withScopedLoader(): () => Provider {
  return () => ({
    provide: LoaderService,
    useClass: LoaderServiceWithQueue,
  });
}
